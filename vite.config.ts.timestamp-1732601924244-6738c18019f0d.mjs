// vite.config.ts
import path2 from "node:path";
import { loadEnv } from "file:///D:/company_project/eros-wap/eros-wap/node_modules/.pnpm/vite@5.2.0_@types+node@20.12.7_sass@1.77.1/node_modules/vite/dist/node/index.js";
import viewport from "file:///D:/company_project/eros-wap/eros-wap/node_modules/.pnpm/postcss-mobile-forever@4.1.5_postcss@8.4.38/node_modules/postcss-mobile-forever/index.js";
import autoprefixer from "file:///D:/company_project/eros-wap/eros-wap/node_modules/.pnpm/autoprefixer@10.4.19_postcss@8.4.38/node_modules/autoprefixer/lib/autoprefixer.js";

// build/vite/index.ts
import { dirname, resolve } from "node:path";
import { fileURLToPath } from "node:url";
import vue from "file:///D:/company_project/eros-wap/eros-wap/node_modules/.pnpm/@vitejs+plugin-vue@5.0.4_vite@5.2.0_@types+node@20.12.7_sass@1.77.1__vue@3.4.21_typescript@5.2.2_/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import AutoImport from "file:///D:/company_project/eros-wap/eros-wap/node_modules/.pnpm/unplugin-auto-import@0.17.5_@vueuse+core@10.9.0_vue@3.4.21_typescript@5.2.2___rollup@4.21.3/node_modules/unplugin-auto-import/dist/vite.js";
import { PrimeVueResolver, VantResolver } from "file:///D:/company_project/eros-wap/eros-wap/node_modules/.pnpm/unplugin-vue-components@0.26.0_@babel+parser@7.25.6_rollup@4.21.3_vue@3.4.21_typescript@5.2.2_/node_modules/unplugin-vue-components/dist/resolvers.js";
import Components from "file:///D:/company_project/eros-wap/eros-wap/node_modules/.pnpm/unplugin-vue-components@0.26.0_@babel+parser@7.25.6_rollup@4.21.3_vue@3.4.21_typescript@5.2.2_/node_modules/unplugin-vue-components/dist/vite.js";
import { VueRouterAutoImports } from "file:///D:/company_project/eros-wap/eros-wap/node_modules/.pnpm/unplugin-vue-router@0.8.6_rollup@4.21.3_vue-router@4.3.2_vue@3.4.21_typescript@5.2.2___vue@3.4.21_typescript@5.2.2_/node_modules/unplugin-vue-router/dist/index.mjs";
import VueRouter from "file:///D:/company_project/eros-wap/eros-wap/node_modules/.pnpm/unplugin-vue-router@0.8.6_rollup@4.21.3_vue-router@4.3.2_vue@3.4.21_typescript@5.2.2___vue@3.4.21_typescript@5.2.2_/node_modules/unplugin-vue-router/dist/vite.mjs";
import mockDevServerPlugin from "file:///D:/company_project/eros-wap/eros-wap/node_modules/.pnpm/vite-plugin-mock-dev-server@1.5.0_rollup@4.21.3_vite@5.2.0_@types+node@20.12.7_sass@1.77.1_/node_modules/vite-plugin-mock-dev-server/dist/index.js";
import { visualizer } from "file:///D:/company_project/eros-wap/eros-wap/node_modules/.pnpm/rollup-plugin-visualizer@5.12.0_rollup@4.21.3/node_modules/rollup-plugin-visualizer/dist/plugin/index.js";
import VueDevTools from "file:///D:/company_project/eros-wap/eros-wap/node_modules/.pnpm/vite-plugin-vue-devtools@7.3.7_rollup@4.21.3_vite@5.2.0_@types+node@20.12.7_sass@1.77.1__vue@3.4.21_typescript@5.2.2_/node_modules/vite-plugin-vue-devtools/dist/vite.mjs";
import VueI18nPlugin from "file:///D:/company_project/eros-wap/eros-wap/node_modules/.pnpm/@intlify+unplugin-vue-i18n@4.0.0_rollup@4.21.3_vue-i18n@9.13.1_vue@3.4.21_typescript@5.2.2__/node_modules/@intlify/unplugin-vue-i18n/lib/vite.mjs";

// build/vite/vconsole.ts
import path from "node:path";
import { viteVConsole } from "file:///D:/company_project/eros-wap/eros-wap/node_modules/.pnpm/vite-plugin-vconsole@2.1.1/node_modules/vite-plugin-vconsole/dist/main.mjs";
function createViteVConsole(isBuild) {
  return viteVConsole({
    entry: [path.resolve("src/main.ts")],
    enabled: true,
    config: {
      maxLogNumber: 1e3,
      theme: "light"
    },
    // https://github.com/vadxq/vite-plugin-vconsole/issues/21
    dynamicConfig: {
      theme: `document.documentElement.classList.contains('dark') ? 'dark' : 'light'`
    },
    eventListener: `
      const targetElement = document.querySelector('html'); // \u62E9\u8981\u76D1\u542C\u7684\u5143\u7D20
      const observerOptions = {
        attributes: true, // \u76D1\u542C\u5C5E\u6027\u53D8\u5316
        attributeFilter: ['class'] // \u53EA\u76D1\u542Cclass\u5C5E\u6027\u53D8\u5316
      };

      // \u5B9A\u4E49\u56DE\u8C03\u51FD\u6570\u6765\u5904\u7406\u89C2\u5BDF\u5230\u7684\u53D8\u5316
      function handleAttributeChange(mutationsList) {
        for(let mutation of mutationsList) {
          if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
            if (window && window.vConsole) {
              window.vConsole.dynamicChange.value = new Date().getTime();
            }
          }
        }
      }

      // \u521B\u5EFA\u89C2\u5BDF\u8005\u5B9E\u4F8B\u5E76\u4F20\u5165\u56DE\u8C03\u51FD\u6570
      const observer = new MutationObserver(handleAttributeChange);

      // \u5F00\u59CB\u89C2\u5BDF\u76EE\u6807\u5143\u7D20
      observer.observe(targetElement, observerOptions);

      // \u5F53\u4E0D\u518D\u9700\u8981\u89C2\u5BDF\u65F6\uFF0C\u505C\u6B62\u89C2\u5BDF
      // observer.disconnect();
    `
  });
}

// build/vite/index.ts
var __vite_injected_original_import_meta_url = "file:///D:/company_project/eros-wap/eros-wap/build/vite/index.ts";
function createVitePlugins(viteEnv, isBuild = false) {
  return [
    // createSvgIcon(isBuild),
    // https://github.com/posva/unplugin-vue-router
    VueRouter({
      extensions: [".vue"],
      routesFolder: "src/pages",
      dts: "src/typed-router.d.ts"
    }),
    vue({
      template: {
        compilerOptions: {
          isCustomElement: (tag) => tag.startsWith("swiper-")
        }
      }
    }),
    // 开启https测试
    // basicSsl(),
    // https://github.com/jbaubree/vite-plugin-sitemap
    // Sitemap(),
    visualizer(),
    // https://github.com/pengzhanbo/vite-plugin-mock-dev-server
    mockDevServerPlugin(),
    // https://github.com/antfu/unplugin-vue-components
    Components({
      extensions: ["vue"],
      resolvers: [VantResolver(), PrimeVueResolver()],
      include: [/\.vue$/, /\.vue\?vue/],
      dts: "src/components.d.ts"
    }),
    // https://github.com/antfu/unplugin-auto-import
    AutoImport({
      include: [/\.[tj]sx?$/, /\.vue$/, /\.vue\?vue/, /\.ts$/],
      imports: [
        "vue",
        "vitest",
        "@vueuse/core",
        "pinia",
        VueRouterAutoImports,
        {
          "vue-router/auto": ["useLink"],
          "@/utils/i18n": ["i18n", "locale"],
          "vue-i18n": ["useI18n"]
        }
        // unheadVueComposablesImports,
      ],
      dts: "src/auto-imports.d.ts"
    }),
    // https://github.com/intlify/bundle-tools/tree/main/packages/unplugin-vue-i18n
    VueI18nPlugin({
      // locale messages resource pre-compile option
      include: resolve(dirname(fileURLToPath(__vite_injected_original_import_meta_url)), "../../src/locales/**")
    }),
    // legacy({
    //   targets: ['defaults', 'not IE 11'],
    // }),
    // https://github.com/antfu/unocss
    // see uno.config.ts for config
    // UnoCSS(),
    // https://github.com/vadxq/vite-plugin-vconsole
    createViteVConsole(isBuild),
    // https://github.com/vuejs/devtools-next
    VueDevTools()
    // https://github.com/antfu/vite-plugin-pwa
    // VitePWA({
    //   registerType: 'autoUpdate',
    //   includeAssets: ['favicon.svg', 'safari-pinned-tab.svg'],
    //   manifest: {
    //     name: 'vue3-vant-mobile',
    //     short_name: 'vue3-vant-mobile',
    //     theme_color: '#ffffff',
    //     icons: [
    //       {
    //         src: '/pwa-192x192.png',
    //         sizes: '192x192',
    //         type: 'image/png',
    //       },
    //       {
    //         src: '/pwa-512x512.png',
    //         sizes: '512x512',
    //         type: 'image/png',
    //       },
    //       {
    //         src: '/pwa-512x512.png',
    //         sizes: '512x512',
    //         type: 'image/png',
    //         purpose: 'any maskable',
    //       },
    //     ],
    //   },
    // }),
  ];
}

// vite.config.ts
var __vite_injected_original_dirname = "D:\\company_project\\eros-wap\\eros-wap";
var vite_config_default = ({ mode, command }) => {
  const root = process.cwd();
  const env = loadEnv(mode, root);
  const { VITE_APP_ENV, VITE_APP_PUBLIC_PATH } = env;
  return {
    base: VITE_APP_PUBLIC_PATH,
    plugins: createVitePlugins(env, command === "build"),
    esbuild: {
      pure: ["console.log"]
    },
    build: {
      rollupOptions: {
        output: {
          manualChunks: () => "everything.js"
        }
      }
    },
    server: {
      host: true,
      port: 8080,
      proxy: {
        "/api": {
          // target: 'https://erosland.ai',
          target: "https://eros-dev.wujialin.top/",
          ws: false,
          changeOrigin: true
          // rewrite: (path) => path.replace(/^\/api/, '')
        }
      }
    },
    resolve: {
      alias: {
        "~@": path2.join(__vite_injected_original_dirname, "./src"),
        "@": path2.join(__vite_injected_original_dirname, "./src"),
        "~": path2.join(__vite_injected_original_dirname, "./src/assets")
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@import 'src/assets/styles/variables';`
        }
      },
      postcss: {
        plugins: [
          autoprefixer(),
          // https://github.com/wswmsword/postcss-mobile-forever
          viewport({
            appSelector: "#app",
            viewportWidth: 375,
            maxDisplayWidth: 500,
            propertyBlackList: ["border", "border-radius"],
            selectorBlackList: ["loading-phone-call", "guide-border", "loading-bar"],
            rootContainingBlockSelectorList: ["van-tabbar", "van-popup", "van-popover", "top-bar"]
          })
        ]
      }
    }
  };
};
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
