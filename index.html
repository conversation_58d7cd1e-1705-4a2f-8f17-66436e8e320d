<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Ugenie | your virtual pal</title>
    <meta
      name="description"
      content="Chat with your AI companion anytime, anywhere, and experience fantastic interactive stories. These highly anthropomorphic chatbots will listen to you, understand you, and remember you. Free to use"
    />
    <meta
      name="keywords"
      content="ugenie, <PERSON>genie, chat AI,  AI chat, chatbot, companion, AI friends, AI companion, AI character, AI roleplay, AI free, AI social tool, interactive stories, anime, virtual idol, live2d, L2D"
    />
    <meta
      property="og:type"
      content="website"
    />
    <meta
      property="og:image"
      content="https://download.static.ogcloud.com/livco-web-static/ugenie_icon.png"
    />
    <meta
      property="og:description"
      content="Chat with your AI companion anytime, anywhere, and experience fantastic interactive stories. These highly anthropomorphic chatbots will listen to you, understand you, and remember you. Free to use"
    />
    <meta
      property="og:title"
      content="Ugenie | your virtual pal"
    />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover"
    />
    <meta
      name="apple-mobile-web-app-capable"
      content="yes"
    />
    <meta
      name="mobile-web-app-capable"
      content="yes"
    />
    <meta
      name="apple-mobile-web-app-status-bar-style"
      content="black"
    />
    <meta
      name="apple-mobile-web-app-title"
      content="Ugenie"
    />
    <link
      rel="apple-touch-icon"
      href="https://download.static.ogcloud.com/livco-web-static/setting/pwa.png"
    />
    <link
      rel="icon"
      type="image/svg+xml"
      href="/ugenie_icon.png"
      sizes="64x64"
    />
    <link
      rel="preload"
      href="https://download.static.ogcloud.com/livco-web-static/font/Roboto-Medium.ttf"
      as="font"
      type="font/ttf"
      crossorigin
    />
    <link
      rel="preload"
      href="https://download.static.ogcloud.com/livco-web-static/font/heiti-sc-medium.woff2"
      as="font"
      type="font/woff2"
      crossorigin
    />
    <link
      rel="preload"
      href="https://download.static.ogcloud.com/livco-web-static/font/Roboto-Regular.ttf"
      as="font"
      type="font/ttf"
      crossorigin
    />
    <link
      rel="preload"
      href="https://download.static.ogcloud.com/livco-web-static/font/Roboto-Bold.ttf"
      as="font"
      type="font/ttf"
      crossorigin
    />
    <link
      rel="preload"
      href="https://download.static.ogcloud.com/livco-web-static/font/Alibaba_PuHuiTi_2.0_105_Heavy_105_Heavy.woff2"
      as="font"
      type="font/woff2"
      crossorigin
    />
    <style>
      @font-face {
        /* 重命名字体名 */
        font-family: Roboto;
        font-style: normal;
        font-weight: 500;

        /* 引入字体 */
        src: url('https://download.static.ogcloud.com/livco-web-static/font/Roboto-Medium.ttf');
        font-display: swap;
      }

      @font-face {
        /* 重命名字体名 */
        font-family: Roboto;
        font-style: normal;
        font-weight: normal;

        /* 引入字体 */
        src: url('https://download.static.ogcloud.com/livco-web-static/font/Roboto-Regular.ttf');
        font-display: swap;
      }

      @font-face {
        /* 重命名字体名 */
        font-family: Roboto;
        font-style: normal;
        font-weight: 600;

        /* 引入字体 */
        src: url('https://download.static.ogcloud.com/livco-web-static/font/Roboto-Bold.ttf');
        font-display: swap;
      }

      @font-face {
        font-family: 'AlibabaPuHuiTi Heavy';
        font-style: normal;
        font-weight: 600;
        src: url('https://download.static.ogcloud.com/livco-web-static/font/Alibaba_PuHuiTi_2.0_105_Heavy_105_Heavy.woff2');
        font-display: swap;
      }

      @font-face {
        font-family: 'Heiti SC';
        font-style: normal;
        font-weight: 600;
        src: url('https://download.static.ogcloud.com/livco-web-static/font/heiti-sc-medium.woff2');
        font-display: swap;
      }

      #initial-loading {
        position: fixed;
        inset: 0;
        z-index: 9999;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        color: #fff;
        background: black;
      }

      .gl-container {
          --uib-size: 40px;
          --uib-color: rgb(235, 223, 172);
          --uib-speed: 1.5s;
          --dot-size: calc(var(--uib-size) * 0.17);

          position: relative;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          width: var(--uib-size);
          height: var(--uib-size);
          animation: smoothRotate calc(var(--uib-speed) * 1.8) linear infinite;
      }

      .dot {
          position: absolute;
          top: 0;
          left: 0;
          display: flex;
          align-items: flex-start;
          justify-content: center;
          width: 100%;
          height: 100%;
          animation: rotate var(--uib-speed) ease-in-out infinite;
      }

      .dot::before {
          width: var(--dot-size);
          height: var(--dot-size);
          content: '';
          background-color: var(--uib-color);
          border-radius: 50%;
          transition: background-color 0.3s ease;
      }

      .dot:nth-child(2),
      .dot:nth-child(2)::before {
          animation-delay: calc(var(--uib-speed) * -0.835 * 0.5);
      }

      .dot:nth-child(3),
      .dot:nth-child(3)::before {
          animation-delay: calc(var(--uib-speed) * -0.668 * 0.5);
      }

      .dot:nth-child(4),
      .dot:nth-child(4)::before {
          animation-delay: calc(var(--uib-speed) * -0.501 * 0.5);
      }

      .dot:nth-child(5),
      .dot:nth-child(5)::before {
          animation-delay: calc(var(--uib-speed) * -0.334 * 0.5);
      }

      .dot:nth-child(6),
      .dot:nth-child(6)::before {
          animation-delay: calc(var(--uib-speed) * -0.167 * 0.5);
      }

      @keyframes rotate {
          0% {
              transform: rotate(0deg);
          }

          65%,
          100% {
              transform: rotate(360deg);
          }
      }

      @keyframes smoothRotate {
          0% {
              transform: rotate(0deg);
          }

          100% {
              transform: rotate(360deg);
          }
      }

      .loading-overlay {
          position: fixed;
          top: 0;
          z-index: 9999;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: calc(100% - 50px);
          background: transparent;
      }

      .loading-box {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 104px;
          height: 104px;
          border-radius: 16px;
          opacity: 0.8;
      }

      .loading {
          width: 56px;
          height: 56px;
          animation: run 1s infinite linear;
      }

      @keyframes run {
          0% {
              transform: rotate(0);
          }

          100% {
              transform: rotate(360deg);
          }
      }
    </style>
    <script>
      ;(function () { document.documentElement.classList.toggle('dark', true) })() /** * // const prefersDark = window.matchMedia &&
window.matchMedia('(prefers-color-scheme: dark)').matches * // const setting = localStorage.getItem('vueuse-color-scheme') || 'auto' * // if (setting === 'dark'
|| (prefersDark && setting !== 'light')) */
    </script>
  </head>
  <body>
    <div id="initial-loading">
      <div class="loading-box">
        <div class="gl-container">
          <div class="dot"></div>
          <div class="dot"></div>
          <div class="dot"></div>
          <div class="dot"></div>
          <div class="dot"></div>
          <div class="dot"></div>
        </div>
      </div>
    </div>
    <div id="app"></div>
    <script
      type="module"
      src="/src/main.ts"
    ></script>

    <script src="https://download.static.ogcloud.com/livco-web-static/setting/live2dcubismcore.min.js"></script>
    <script src="https://download.static.ogcloud.com/livco-web-static/setting/live2d.min.js"></script>

    <noscript> This website requires JavaScript to function properly. Please enable JavaScript to continue. </noscript>
  </body>
</html>
