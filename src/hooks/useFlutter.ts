// import { useAppStore, useUserStore } from '@/stores'
// import { filterDeviceAttr } from '@/utils'
// import { checkVersion } from '/utils/versionUpdate'
import useAppStore from '@/stores/modules/app'
class Flutter {
  constructor() {
    // 获取adjust渠道源
    // setTimeout(() => {
    //   window.flutter_inappwebview && window.flutter_inappwebview.callHandler('JsAndroid', JSON.stringify({ method: 'adjustData' }))
    // }, 2800)
  }
  registerEvent() {
    // console.log(t)
    const appStore = useAppStore()
    const getRequestHeader = () => {
      return JSON.stringify({
        // version: appStore.systemInfo.appWgtVersion || appStore.systemInfo.appVersion,
        'access-token': localStorage.getItem('access_token'),
        'login-id': localStorage.getItem('login_id'),
        terminal: appStore.terminal
        // 'android-device-info': encodeURIComponent(btoa(JSON.stringify(filterDeviceAttr(appStore.systemInfo))))
      })
    }

    const getBackToastTip = () => {
      return JSON.stringify({
        back_tip: '再按一次退出应用'
      })
    }
    // 获取归因渠道
    // const getDdJustDataResult = (data) => {
    //   appStore.adjustAttribution = data
    // }
    // 根 flutter webview 获取请求头
    ;(window as any).getRequestHeader = getRequestHeader
    // 根 flutter webview 获取返回提示
    ;(window as any).getBackToastTip = getBackToastTip

    // window.adJustDataResult = getDdJustDataResult
  }
  // initTopOnAds() {
  //   if (!window.flutter_inappwebview) return
  //   const adStore = useAdStore()
  //   window.loadAdSuccess = () => {
  //     adStore.setFlutterAdsStatus(5)
  //   }
  //   this.getTopOnAds('1')
  // }
  // showKeyBoard() {
  //   const appStore = useAppStore()
  //   window.getKeyBoardHeight = (keyBoardHeight) => {
  //     appStore.keyboardHeight = keyBoardHeight
  //     localStorage.setItem('keyBoardHeight', keyBoardHeight)
  //   }
  //   window.flutter_inappwebview.callHandler('JsAndroid', JSON.stringify({ method: 'showKeyBoard' }))
  // }
  // getTopOnAds(type) {
  //   window.flutter_inappwebview.callHandler('JsAndroid', JSON.stringify({ method: 'topOnAds', type, data: uni.getStorageSync('loginId') }))
  // }

  loginByThird(type: string) {
    return new Promise((resolve, reject) => {
      ;(window as any).loginByThirdResult = (res: any) => {
        const { status, data } = res
        if (status === 1) {
          resolve(data)
        } else {
          reject()
        }
      }
      ;(window as any).flutter_inappwebview.callHandler('JsAndroid', JSON.stringify({ method: 'loginByThird', type }))
    })
  }

  saveImage(url: string) {
    return new Promise((resolve) => {
      // status 1 成功   2失败
      ;(window as any).saveImageResult = (status: any) => {
        resolve(status)
      }
      ;(window as any).flutter_inappwebview.callHandler('JsAndroid', JSON.stringify({ method: 'saveImage', data: url }))
    })
  }

  share(url: string) {
    return new Promise((resolve) => {
      // status 1 成功   2取消  3 不合法
      ;(window as any).shareImageResult = (status: number) => {
        resolve(status)
      }
      ;(window as any).flutter_inappwebview.callHandler('JsAndroid', JSON.stringify({ method: 'shareImage', data: url }))
    })
  }

  mediaAdd() {
    return new Promise((resolve) => {
      ;(window as any).selectImagesFile = (res: string[]) => {
        resolve(res)
      }
      ;(window as any).flutter_inappwebview.callHandler('JsAndroid', JSON.stringify({ method: 'mediaAdd' }))
    })
  }
  isIphoneX() {
    return new Promise((resolve) => {
      ;(window as any).isIphoneX = (res: any) => {
        resolve(res)
      }
      ;(window as any).flutter_inappwebview.callHandler('JsAndroid', JSON.stringify({ method: 'isIphoneX' }))
    })
  }
  iOSAppTracking() {
    return new Promise((resolve) => {
      ;(window as any).iosAppTrack = (res: number) => {
        resolve(res)
      }
      ;(window as any).flutter_inappwebview.callHandler('JsAndroid', JSON.stringify({ method: 'iOSAppTracking' }))
    })
  }

  audioRecordPermission() {
    return new Promise((resolve) => {
      ;(window as any).callAudioRecordPermission = (res: number) => {
        resolve(res)
      }
      ;(window as any).flutter_inappwebview.callHandler('JsAndroid', JSON.stringify({ method: 'audioRecordPermission' }))
    })
  }

  applePay(product_id: string, orderId: string) {
    console.log('applePayParams', product_id, orderId)

    return new Promise((resolve, reject) => {
      ;(window as any).applePaySuccess = (res: any) => {
        console.log('applePaySuccess', res)
        resolve(JSON.parse(res))
      }
      ;(window as any).applePayFail = (res: number) => {
        console.log('applePayFail', res)
        reject(res)
      }
      ;(window as any).flutter_inappwebview.callHandler('JsAndroid', JSON.stringify({ method: 'applePay', data: product_id, orderId }))
    })
  }
  googlePay(product_id: string, orderId: string) {
    return new Promise((resolve, reject) => {
      ;(window as any).googlePaySuccess = (res: string) => {
        resolve(res)
      }
      ;(window as any).googlePayFail = (res: number) => {
        reject(res)
      }
      ;(window as any).flutter_inappwebview.callHandler('JsAndroid', JSON.stringify({ method: 'googlePay', data: product_id, orderId }))
    })
  }

  // trackEvent(data) {
  //   if (window.flutter_inappwebview) {
  //     window.flutter_inappwebview.callHandler('JsAndroid', JSON.stringify({ method: 'adjustEvent', data: data.eventToken }))
  //   }
  // }

  getInfoSystemInfo = () => {
    return new Promise((resolve) => {
      ;(window as any).infoDataResult = (info: any) => {
        console.log(info, 'kkkk')
        const appStore = useAppStore()
        // const userStore = useUserStore()
        // const versionStore = useVersionStore()
        appStore.setSystemInfo({ ...info, windowHeight: info.screenHeight, windowWidth: info.screenWidth })
        // if (userStore.token) {
        //   versionStore.getLatestVersionHandle('home')
        // }
        resolve(info)
      }
      setTimeout(() => {
        ;(window as any).flutter_inappwebview.callHandler('JsAndroid', JSON.stringify({ method: 'infoData' }))
      }, 2000)
    })
  }

  jumpStore() {
    ;(window as any).flutter_inappwebview.callHandler('JsAndroid', JSON.stringify({ method: 'jumpStore' }))
  }
  openUrl(data: string) {
    ;(window as any).flutter_inappwebview.callHandler('JsAndroid', JSON.stringify({ method: 'jumpBrowser', data }))
  }
}

export default new Flutter()
