<script setup lang="ts">
import Recorder from 'recorder-core/recorder.mp3.min.js'
import '../../../scripts/frequency.histogram.view.cjs'
import 'recorder-core/src/extensions/lib.fft.js'
import 'recorder-core/src/extensions/buffer_stream.player.js'
import anime from 'animejs'
import { vOnLongPress } from '@vueuse/components'
import { PopoverPlacement } from 'vant'
import { cloneDeep, debounce } from 'lodash-es'
import { Howl } from 'howler'
import { useIntersectionObserver } from '@vueuse/core'

import { FAKE_COMMENT_END_FLAG, FAKE_COMMENT_START_FLAG, handleCacheChat, handleFakeCommentRecord } from '@/hooks/useHandleStream.ts'
import { StringKeyValueTType } from '@/types'
import { useModal } from '@/hooks/useModal.ts'
import {
  bgmSetting,
  blockAgentAudio,
  blockAgentTextChat,
  getChatRecord,
  getDressByID,
  getThreeDSource,
  likeOrUnlikeAgentText,
  startNewChat,
  webSearchTool
} from '@/api/agentChat'
import { ResultEnum } from '@/enums/httpEnum.ts'
import useUserStore from '@/stores/modules/user.ts'
import { BOOL_NUMBER, CHAT_BOX_STYLE_CLASS } from '@/constant'
import { copyText, getMarkdownImageUrl, markdownToHtml, playChatAudio, replaceBracketsWithAsterisks, scrollToBottom } from '@/utils'
import { AgentMessageType } from '@/api/agentHomePage/types.ts'
import { getAIInfo } from '@/api/agentHomePage'
import { IGiftListType, SelfieType, ThreeDSourceType, ToolsType } from '@/api/agentChat/types.ts'
import { DressType, SkinTypeEnum } from '@/api/propertyStore/types.ts'
import { useBackHistory } from '@/hooks/useCommon.ts'
import { eventReport, EventTypeEnum, ForwardAddressEnum, InteractiveTypeEnum } from '@/api/eventReport'
import { ChatRecordTypeEnum, ICurrencyTypeEnum } from '@/enums'
import { POPUP_TYPE_NAME, usePopupMemoryStore } from '@/stores/modules/popupMemory.ts'
import { useConfirm } from '@/hooks/useConfirm.ts'
import { ugPhoneLinkSdk } from '@/utils/ugPhoneLinkSdk.ts'

const popupMemoryStore = usePopupMemoryStore()
const { t } = useI18n()
const bgmHowlerInstance = ref<Howl | null>(null)
const live2dClear = ref<HTMLElement | null>(null)
const chatPageRef = ref<HTMLElement | null>(null)
const l2dRef = ref(null)
const isClearMode = ref(false)
const isFirstInNoAction = ref(true)
const isNoRecord = ref(false)

interface ChatRecordType {
  timestamp?: number
  type: ChatRecordTypeEnum
  content: string
  message_id?: string
  replayTime?: number
  // 一个数组可能有0,1,2个字符串元素
  // replayContent?: [string?, string?]
  searchContent?: ToolsType[]
  is_search?: boolean
  is_like?: number
  is_bad?: number
  is_fake_comment?: boolean
  is_phone_call?: number
  is_later?: number
  audio_file_url?: string
  audio_length?: number
  call_time?: number
  ttsLoading?: boolean
  ttsPlaying?: boolean
  textLoading?: boolean
  userTTS?: boolean
  photoDetail?: SelfieType
  photo_id?: number
  content_id?: number
  currency_type?: ICurrencyTypeEnum
  price?: number
  gift_info?: IGiftListType
}

enum QueryTypeEnum {
  TIPS = 3,
  UPGRADE = 101,
  ENDING = 102,
  TOOL = 103
}
const runningContextAudioForPhone = ref()
const userStore = useUserStore()
const route = useRoute()
const CANCEL_DISTANCE = 60
const router = useRouter()
// 是否取消发送语音
const isCancelDistance = computed(() => {
  // 两个值有一个为0应该返回false
  return Math.abs(slidePoint.start - slidePoint.end) > CANCEL_DISTANCE && slidePoint.start !== 0 && slidePoint.end !== 0
})

// @ts-ignore
const aiID = ref(route.params.ai_id as number)

const agentMessage = ref<AgentMessageType>({
  tag_list: [],
  like_status: 0,
  ai_chat_setting: {
    image_id: undefined,
    show_disclaimer: 0,
    show_character: 0,
    show_prologue: 0,
    show_muse: 0,
    intimacy_now: 0,
    intimacy_upgrade: 0,
    intimacy_level: 0,
    intimacy_interest_tags: []
  },
  opening_option: [],
  id: undefined,
  name: undefined,
  follow: '0',
  has_3d: undefined,
  is_repeat: undefined,
  chat_times: '0',
  image_url: undefined,
  camera_pic_status: undefined,
  init_plot: undefined,
  bgm: '',
  bg_info: {
    bg_url: undefined,
    set_id: undefined,
    dress_id: undefined,
    type: undefined
  },
  update_time: undefined,
  sound_ray_url: undefined,
  opening_statement_voice: undefined,
  intimacy_level_info: [],
  description: undefined,
  api_key: undefined,
  dataset_id: undefined,
  app_id: undefined,
  sex: undefined,
  role_setting: undefined,
  opening_statement: undefined,
  back_story: undefined,
  talk_example: undefined,
  synopsis: undefined,
  voice_id: undefined,
  is_show: 0,
  is_quick: 0,
  last_set_id: undefined,
  status: 0,
  avatar_url: undefined,
  client_image_id: undefined,
  terminal: undefined,
  client_info: {
    client_id: undefined,
    name: undefined,
    email: undefined,
    avatar_url: undefined
  },
  admin_info: {
    admin_id: undefined,
    email: undefined,
    name: undefined,
    avatar_url: undefined
  },
  resident_function: [],
  resident_type: 3
})

const clickTime = reactive({
  start: 0,
  end: 0
})
const loadingStatus = reactive({
  chatRecord: false, // 加载聊天记录
  tts: false, // 语音转文字
  streaming: false, // 流式请求中
  textOutputting: false, // 文字输出中
  retrying: false,
  record: false, // 录音
  feedback: false // 点赞点踩loading
})
const flagStatus = reactive({
  // 半屏状态
  halfScreen: false,
  // 是否出现向下滚动
  showScrollDown: false,
  // 打电话
  phoneCall: false,
  // live2d显示
  live2d: false,
  // 预览
  previewL2D: false,
  // 麦克风授权状态
  micAllow: false,
  // 键盘输入or语音输入
  inputting: true
})
const live2dStatus = computed(() => {
  return flagStatus.live2d
})
const showPopover = ref<StringKeyValueTType<boolean>>({})
const isCollapse = ref(false)
const question = ref('')

const slidePoint = reactive({
  start: 0,
  end: 0
})

const guideForClearModeVisible = ref(false)
const guideForTouchVisible = ref(false)
const topUpVisible = ref(false)
const giftVisible = ref(false)
const permissionVisible = ref(false)
const skinVisible = ref(false)

const myRecorder = ref(null)
const myStream = ref(null)
const myHistogram = ref(null)
const heartRef = ref(null)
const chatRef = ref(null)
const talkBtnRef = ref(null)
const collapseContentRef = ref(null)
const replayItemRef = ref(null)
const countDownRef = ref(null) // 倒计时ref

const l2dEmo = ref('')
const chatRecord = ref<ChatRecordType[]>([])
const skinList = ref<DressType[]>([])

const live2dResource = ref<ThreeDSourceType>({
  model_json: undefined,
  action_list: [],
  rotate: 0
})

const getUniqueChatItem = (timestamp: number, chatType: ChatRecordTypeEnum): ChatRecordType => {
  if (chatRecord.value[chatRecord.value.length - 1].timestamp !== timestamp || chatRecord.value[chatRecord.value.length - 1].type !== chatType) {
    return chatRecord.value.findLast((item) => item.timestamp === timestamp && item.type === chatType)
  } else {
    return chatRecord.value[chatRecord.value.length - 1]
  }
}

const getUniqueChatItemIndex = (timestamp: number, chatType: ChatRecordTypeEnum) => {
  if (chatRecord.value[chatRecord.value.length - 1].timestamp !== timestamp || chatRecord.value[chatRecord.value.length - 1].type !== chatType) {
    return chatRecord.value.findLastIndex((item) => item.timestamp === timestamp && item.type === chatType)
  } else {
    return chatRecord.value.length - 1
  }
}

// 录音初始化
const recOpenInit = (success: typeof recStart) => {
  if (!myRecorder.value) {
    myRecorder.value = new Recorder({
      type: 'mp3',
      sampleRate: 16000,
      bitRate: 16,
      onProcess: function (buffers: any, powerLevel: number, _: number, bufferSampleRate: number) {
        myHistogram.value.input(buffers[buffers.length - 1], powerLevel, bufferSampleRate)
      }
    })
  }
  myRecorder.value.open(
    function () {
      flagStatus.micAllow = true
      //打开麦克风授权获得相关资源
      success && success()
    },
    function (msg: string, isUserNotAllow: boolean) {
      //用户拒绝未授权或不支持
      if (isUserNotAllow) {
        useModal({
          message: t('recordingPermissionError'),
          duration: 1000
        })
      } else {
        console.error(msg)
      }
    }
  )
}

// 录音开始
const recStart = () => {
  controlBgm(false, true) // 停止BGM
  loadingStatus.record = true
  nextTick(() => {
    if (!myHistogram.value) {
      myHistogram.value = Recorder.FrequencyHistogramView({
        elem: '.wave',
        lineCount: 15,
        linear: [0, '#2f2101', 1, '#2f2101'],
        position: 0,
        widthRatio: 0.4,
        mirrorEnable: true,
        stripeEnable: false,
        minHeight: 3
      })
    }
    myRecorder.value.start()
    countDownRef.value.start()
  })
}

// 录音结束
const recStop = (isCancel: boolean) => {
  controlBgm() // 恢复BGM
  myRecorder.value.stop(
    function (blob: Blob, duration: number) {
      loadingStatus.record = false
      console.log(blob, '时长:' + duration + 'ms')
      myRecorder.value.close() //释放录音资源，当然可以不释放，后面可以连续调用start；但不释放时系统或浏览器会一直提示在录音，最佳操作是录完就close掉
      myRecorder.value = null
      if (duration < 500) {
        // useModal({
        //   message: t('speechTooShort')
        // })
        return
      }
      if (!isCancel) {
        let timestamp = Date.now()
        //已经拿到blob文件对象想干嘛就干嘛：立即播放、上传、下载保存
        const file = new File([blob], 'record.mp3')
        const formData = new FormData()
        startChatReport(InteractiveTypeEnum.CHAT)
        formData.append('audio_file', file)
        formData.append('ai_id', String(aiID.value))
        formData.append('interactive_model', String(live2dStatus.value ? 2 : 1))
        // loadingStatus.streaming = true
        chatRecord.value.push({
          type: ChatRecordTypeEnum.USER,
          timestamp,
          content: '',
          is_like: BOOL_NUMBER.NO,
          is_bad: BOOL_NUMBER.NO,
          is_phone_call: BOOL_NUMBER.NO,
          message_id: '',
          userTTS: false
        })
        loadingStatus.tts = true
        scrollToBottom(chatRef.value)
        getUniqueChatItem(timestamp, ChatRecordTypeEnum.USER).userTTS = true
        blockAgentAudio(formData)
          .then(({ code, data }) => {
            if (code === ResultEnum.SUCCESS) {
              getUniqueChatItem(timestamp, ChatRecordTypeEnum.USER).content = data
              getUniqueChatItem(timestamp, ChatRecordTypeEnum.USER).userTTS = false
              scrollToBottom(chatRef.value)
              blockSendQuestion(data)
            }
          })
          .catch((err) => {
            chatRecord.value.splice(getUniqueChatItemIndex(timestamp, ChatRecordTypeEnum.USER), 1)
            console.log(err)
          })
          .finally(() => {
            loadingStatus.tts = false
          })
      }
    },
    function (msg: string) {
      console.log('录音失败:' + msg)
      myRecorder.value.close() //可以通过stop方法的第3个参数来自动调用close
      myRecorder.value = null
    }
  )
}

// 进入打电话
const enterPhoneCall = () => {
  useModal({
    message: t('featureCoding')
  })

  startChatReport(InteractiveTypeEnum.CALL)
  return
  // if (!agentMessage.value.ai_chat_setting.intimacy_interest_tags.includes('phone_call')) {
  //   useModal({
  //     message: t('intimacyUnlockFunc')
  //   })
  //   return
  // }
  // if (userStore.userInformation.phone_time <= 0) {
  //   topUpVisible.value = true
  //   return
  // }
  // if (flagStatus.micAllow) {
  //   runningContextAudioForPhone.value = Recorder.GetContext(true)
  //   Howler.stop()
  //   flagStatus.phoneCall = true
  //   startChatReport(InteractiveTypeEnum.CALL)
  // } else {
  //   permissionVisible.value = true
  // }
}

const phoneCallPermission = () => {
  recOpenInit(() => {
    myRecorder.value.close()
    myRecorder.value = null
    Howler.stop()
    flagStatus.phoneCall = true
  })
}

const startChatReport = (type: InteractiveTypeEnum) => {
  if (isFirstInNoAction.value) {
    isFirstInNoAction.value = false
    eventReport({
      event_type: isNoRecord.value ? EventTypeEnum.START_CHAT : EventTypeEnum.CONTINUE_CHAT,
      ai_id: aiID.value,
      interactive_type: type
    }).catch((err) => {
      console.warn(err)
    })
  }
}

// 倒计时结束
const countDownFinish = () => {
  recStop(false)
}

const exitClearMode = () => {
  isClearMode.value = false
  eventReport({
    event_type: EventTypeEnum.EXIT_IMMERSE_MODE,
    ai_id: aiID.value
  }).catch((err) => {
    console.warn(err)
  })
  chatPageRef.value.scrollIntoView({ behavior: 'smooth' })
}

const handleGuideClearMode = () => {
  if (!userStore.newerOption.hadSwitchToClearMode && live2dStatus.value) {
    guideForClearModeVisible.value = true
    userStore.newerOption.hadSwitchToClearMode = true
    eventReport({
      event_type: EventTypeEnum.SHOW_CLEAR_SCREEN_TUTORIAL,
      ai_id: aiID.value
    }).catch((err) => {
      console.warn(err)
    })
  }
}

const handleGuideTouch = () => {
  if (!userStore.newerOption.hadTouch) {
    guideForTouchVisible.value = true
    userStore.newerOption.hadTouch = true
    eventReport({
      event_type: EventTypeEnum.SHOW_TOUCH_ACTION_TUTORIAL,
      ai_id: aiID.value
    }).catch((err) => {
      console.warn(err)
    })
  }
}

const restartChat = () => {
  useConfirm({
    content: t('resetCharacterNotice'),
    confirmText: t('confirmButton'),
    cancelText: t('cancelButton'),
    onConfirm: () => {
      const { close } = useModal({
        loading: true,
        message: t('restarting'),
        autoClose: false
      })
      startNewChat({ ai_id: aiID.value })
        .then((res) => {
          const { code } = res
          if (code === 200) {
            useModal({
              message: t('restartSuccessful')
            })
            getAgentRecord()
          }
        })
        .catch((err) => {
          console.warn(err)
        })
        .finally(() => {
          close()
        })
    }
  })
}

/**
 * 控制BGM的播放状态
 * @param forcePlay 强制播放
 * @param forcePause 强制暂停
 */
const controlBgm = (forcePlay?: boolean, forcePause?: boolean) => {
  // 如果没有实例或没有bgm，直接返回
  if (!bgmHowlerInstance.value || !agentMessage.value.bgm) return

  const shouldPlay = forcePlay ?? (userStore.userInformation.bgm && document.visibilityState === 'visible')
  const shouldPause = forcePause ?? !shouldPlay
  if (shouldPlay) {
    bgmHowlerInstance.value.play()
  } else if (shouldPause) {
    Howler.stop()
    bgmHowlerInstance.value.pause()
  }
}

/**
 * 初始化BGM
 */
const initBgm = () => {
  if (agentMessage.value.bgm && !bgmHowlerInstance.value) {
    bgmHowlerInstance.value = new Howl({
      src: agentMessage.value.bgm,
      loop: true
    })
    controlBgm()
  }
}

/**
 * 切换BGM开关状态
 */
const setBgmStatus = () => {
  bgmSetting({
    bgm: Boolean(userStore.userInformation.bgm) ? 0 : 1
  }).then((res) => {
    if (res.code === ResultEnum.SUCCESS) {
      userStore.userInformation.bgm = Boolean(userStore.userInformation.bgm) ? 0 : 1
      controlBgm()
    }
  })
}

const debounceSetBgmStatus = debounce(setBgmStatus, 1000, {
  leading: true,
  trailing: false
})

/**
 * 处理语音播放时的BGM
 */
const handleVoicePlay = (voicePath: string) => {
  if (!userStore.userInformation.bgm) return

  bgmHowlerInstance.value?.fade(1, 0.3, 1000)

  if (aiID.value.toString() === '100405') {
    l2dRef.value.erosModel.triggerMotionSync(voicePath, {
      onStop: () => {
        bgmHowlerInstance.value?.fade(0.3, 1, 1000)
      }
    })
  } else {
    playChatAudio(voicePath, saveSound, {
      onEnd: () => {
        bgmHowlerInstance.value?.fade(0.3, 1, 1000)
      }
    })
  }
}

const goHomePage = () => {
  eventReport({
    event_type: EventTypeEnum.OPEN_AI_PROFILE,
    ai_id: aiID.value
  }).catch((err) => {
    console.warn(err)
  })
  router.push(`/agentHomePage/${aiID.value}`)
}

const goBack = () => {
  eventReport({
    event_type: EventTypeEnum.EXIT_CHAT,
    ai_id: aiID.value
  }).catch((err) => {
    console.warn(err)
  })
  if (route.query.path === 'create') {
    router.push('/')
  } else {
    useBackHistory()
  }
}

const onLongPressCallbackDirective = (item: ChatRecordType, index: number) => {
  if (item.type === ChatRecordTypeEnum.AI) {
    showPopover.value[index] = true
  }
}

const dividePlacement = (type: ChatRecordTypeEnum): PopoverPlacement => {
  switch (type) {
    case ChatRecordTypeEnum.USER:
      return 'top-end'
    case ChatRecordTypeEnum.AI:
      return 'top-start'
    default:
      return 'top-end'
  }
}

const handleCollapse = (flag: boolean = false) => {
  if (flag) {
    debounceSendMessage()
    return
  } else {
    if (collapseContentRef.value.style.maxHeight) {
      closeCollapse()
    } else {
      flagStatus.inputting = true
      openCollapse()
    }
  }
}

const closeCollapse = () => {
  if (!isCollapse.value) return
  collapseContentRef.value.style.maxHeight = null
  collapseContentRef.value.style.marginTop = '0'
  scrollToBottom(chatRef.value)
  anime({
    targets: '.switch-mode',
    rotate: [0, 90],
    duration: 100,
    easing: 'linear',
    complete: () => {
      isCollapse.value = false
    }
  })
}

const openCollapse = () => {
  collapseContentRef.value.style.maxHeight = collapseContentRef.value.scrollHeight + 'px'
  collapseContentRef.value.style.marginTop = '16px'
  anime({
    targets: '.switch-mode',
    rotate: [90, 0],
    duration: 100,
    easing: 'linear',
    complete: () => {
      isCollapse.value = true
    }
  })
}

const changeInputting = () => {
  if (flagStatus.inputting) {
    closeCollapse()
  }
  flagStatus.inputting = !flagStatus.inputting
}

const getDress = () => {
  getDressByID({
    ai_id: aiID.value
  })
    .then((res) => {
      const { code, data } = res
      if (code === 200) {
        skinList.value = data
      }
    })
    .catch((err) => {
      console.warn(err)
    })
    .finally(() => {})
}

const getAgentMessageAndRecord = (record?: boolean) => {
  getAIInfo({ ai_id: aiID.value }).then((res) => {
    const { code, data, msg } = res
    if (code === 200) {
      agentMessage.value = data
      if (record && data.bg_info.type === SkinTypeEnum.ThreeD) {
        getThreeDResource(data.bg_info.dress_id)
      }
      if (record) {
        getAgentRecord()
      }
      // 初始化BGM
      initBgm()
    } else if (code === ResultEnum.ABSENT_AGENT) {
      useModal({
        message: msg,
        duration: 1500,
        onClose: () => {
          router.push('/')
        }
      })
    } else if (code === ResultEnum.UN_EXISTENCE) {
      router.replace('/404')
    }
  })
}

const getAgentRecord = () => {
  getChatRecord({
    ai_id: aiID.value,
    limit: 9999,
    page: 1
  }).then((res) => {
    const { code, data } = res
    if (code === 200) {
      let record: ChatRecordType[] = []
      // 没记录，显示ai生成提示
      if (data.list.filter((item) => !item.content_info).length === 0) {
        isNoRecord.value = true
        record = [
          {
            type: ChatRecordTypeEnum.WARN,
            content: '',
            is_like: BOOL_NUMBER.NO,
            is_bad: BOOL_NUMBER.NO,
            message_id: '',
            is_phone_call: BOOL_NUMBER.NO,
            replayTime: 0
            // replayContent: []
          }
        ]
      }
      // 剧情和开场白
      if (agentMessage.value.init_plot) {
        record.push({
          type: ChatRecordTypeEnum.SYNOPSIS,
          content: agentMessage.value.init_plot
        })
      }
      if (agentMessage.value.opening_statement) {
        record.push({
          type: ChatRecordTypeEnum.AI,
          content: agentMessage.value.opening_statement,
          is_like: BOOL_NUMBER.NO,
          is_bad: BOOL_NUMBER.NO,
          is_phone_call: BOOL_NUMBER.NO,
          message_id: '',
          replayTime: 0,
          // replayContent: [],
          ttsLoading: false,
          ttsPlaying: false,
          audio_file_url: agentMessage.value.opening_statement_voice,
          audio_length: Number((agentMessage.value.opening_statement.length * 0.15).toFixed(0))
        })
      }

      if (agentMessage.value.opening_statement_voice && data.list.length === 0) {
        playChatAudio(agentMessage.value.opening_statement_voice)
      }
      const tempList = cloneDeep(data.list)
      // 记录列表
      tempList.reverse().map((item) => {
        const baseRecord = {
          is_like: item.is_like,
          is_bad: item.is_bad,
          message_id: item.message_id,
          is_phone_call: item.is_phone_call,
          call_time: item.call_time,
          replayTime: 0,
          // replayContent: [],
          ttsLoading: false,
          audio_file_url: item?.audio_file_url,
          audio_length: item?.audio_length
        }
        // 语音记录同步提示
        if (item.is_phone_call) {
          record.push({
            ...baseRecord,
            type: ChatRecordTypeEnum.PHONE,
            content: t('syncPhoneCallRecord')
          })
          return
        }

        // 继续未完成的内容玩法提示
        if (item.input_query === 'continue_content') {
          // record.push({
          //   ...baseRecord,
          //   type: ChatRecordTypeEnum.KEEP_TALK,
          //   content: t('Talking last time：') + item.content_info.name,
          //   content_id: item.content_id
          // })
          return
        }

        // 重启对话提示
        if (item.output_type === QueryTypeEnum.TIPS) {
          record.push({
            ...baseRecord,
            type: ChatRecordTypeEnum.TIPS,
            content: t('newChatStartedNotice')
          })

          // 有开场白在重启后面加一句开场白
          if (agentMessage.value.opening_statement) {
            record.push({
              ...baseRecord,
              type: ChatRecordTypeEnum.AI,
              content: agentMessage.value.opening_statement,
              is_like: BOOL_NUMBER.NO,
              is_bad: BOOL_NUMBER.NO,
              is_phone_call: BOOL_NUMBER.NO,
              message_id: '',
              replayTime: 0,
              ttsPlaying: false,
              audio_file_url: agentMessage.value.opening_statement_voice,
              audio_length: Number((agentMessage.value.opening_statement?.length * 0.15).toFixed(0))
            })
          }
          return
        }

        // 好感度玩法已解锁提示
        if (item.input_query === 'unlocked_content') {
          // if (item.is_later === BOOL_NUMBER.NO) {
          //   flagStatus.showContentGameBtn = true
          //   showContentGameMessage.value.sessionID = item.session_id
          //   showContentGameMessage.value.contentID = item.content_id
          //   showContentGameMessage.value.goBtnText = item.content_info?.into_button
          //   showContentGameMessage.value.laterBtnText = item.content_info?.close_button
          // }
          // record.push({
          //   ...baseRecord,
          //   type: ChatRecordTypeEnum.STORY_UNLOCK,
          //   content: `LV${item?.content_info?.level} exclusive gameplay has been unlocked`,
          //   is_later: item.is_later,
          //   content_id: item.content_id
          // })
          return
        }

        // 好感度升级提示
        if (item.input_type === QueryTypeEnum.UPGRADE) {
          // record.push({
          //   ...baseRecord,
          //   type: ChatRecordTypeEnum.INTIMACY_UPGRADE,
          //   content: `Intimacy level upgraded to LV${item.input_query}`
          // })
          return
        }

        // 故事结束同步记录提示
        if (item.input_type === QueryTypeEnum.ENDING) {
          record.push({
            ...baseRecord,
            type: ChatRecordTypeEnum.TIPS,
            content: t('syncGameMemory', { level: item.content_info.level })
            // content: `已同步LV${item.content_info.level}专属玩法记忆`
          })
          record.push({
            ...baseRecord,
            type: ChatRecordTypeEnum.AI,
            content: item.output_text
          })
          return
        }

        if (item.input_type === QueryTypeEnum.TOOL) {
          record.push({
            ...baseRecord,
            type: ChatRecordTypeEnum.AI,
            content: item.output_text
          })
          return
        }

        // 自拍图
        if (item.pic_id && item.pic_info?.pic_url) {
          record.push({
            ...baseRecord,
            type: ChatRecordTypeEnum.IMAGE,
            content: item.pic_info?.pic_url || item.pic_info?.vague_pic_url,
            photoDetail: item.pic_info
          })
          return
        }

        // 内容玩法提示图与开场白
        if (item.input_query === 'start_content') {
          record.push({
            ...baseRecord,
            content_id: item.content_id,
            type: ChatRecordTypeEnum.STORY_IMAGE,
            content: item.content_info?.prefix_url + item.content_info?.cover_url
          })
          record.push({
            ...baseRecord,
            type: ChatRecordTypeEnum.TIPS,
            content: t('clickToStory')
          })
          if (item.content_info?.opening_statement_outside) {
            record.push({
              ...baseRecord,
              type: ChatRecordTypeEnum.AI,
              content: item.content_info?.opening_statement_outside || ''
            })
          }
          return
        }

        // 对话
        if (item.input_query) {
          // 送礼提示语不显示
          if (item.send_gift !== BOOL_NUMBER.YES && item.input_query !== '☆') {
            if (item.input_query.includes('|')) {
              handleCacheChat(item.input_query).map((item) => {
                record.push({
                  ...baseRecord,
                  type: ChatRecordTypeEnum.USER,
                  content: item
                })
              })
            } else {
              record.push({
                ...baseRecord,
                type: ChatRecordTypeEnum.USER,
                content: item.input_query
              })
            }
          }
        }

        if (item.output_text) {
          if (item.output_text.includes(FAKE_COMMENT_START_FLAG) || item.output_text.includes(FAKE_COMMENT_END_FLAG)) {
            // console.log(handleFakeCommentRecord(item.output_text))
            handleFakeCommentRecord(item.output_text).map((item) => {
              record.push({
                ...baseRecord,
                audio_file_url: undefined,
                type: ChatRecordTypeEnum.AI,
                content: item,
                is_fake_comment: true,
                audio_length: Number((item?.length * 0.15).toFixed(0))
              })
            })
          } else {
            if (item.gift_info) {
              record.push({
                ...baseRecord,
                type: ChatRecordTypeEnum.GIFT,
                gift_info: item.gift_info,
                content: t('send') + `${item.gift_info.name}，`
              })
            }
            record.push({
              ...baseRecord,
              type: ChatRecordTypeEnum.AI,
              content: item.output_text
            })
          }
        }
      })

      chatRecord.value = record
      scrollToBottom(chatRef.value, 'instant')
    }
  })
}

const getThreeDResource = (id: number) => {
  getThreeDSource({
    id
  }).then((res) => {
    const { data, code } = res
    if (code === ResultEnum.SUCCESS) {
      live2dResource.value = data
      flagStatus.live2d = true
      handleGuideClearMode()
      nextTick(() => {
        l2dRef.value.createNewModel()
      })
    }
  })
}

const permissionInit = () => {
  // @ts-ignore
  navigator.permissions?.query({ name: 'microphone' }).then((res) => {
    if (res.state === 'granted') {
      flagStatus.micAllow = true
    }
  })
  const popup = popupMemoryStore.getPopupMemory()
  if (popup) {
    switch (popup.type) {
      case POPUP_TYPE_NAME.BUY_SKIN:
        nextTick(() => {
          skinVisible.value = true
        })
        break
      case POPUP_TYPE_NAME.SEND_GIFT:
        nextTick(() => {
          giftVisible.value = true
          popupMemoryStore.resetAllPopupMemory()
        })
        break
      // case POPUP_TYPE_NAME.BUY_SELFIE:
      //   unlockVisible.value = true
      //   break
      case POPUP_TYPE_NAME.BUY_CALL_TIME:
        topUpVisible.value = true
        popupMemoryStore.resetAllPopupMemory()
        break
    }
  }
  loadingStatus.record = true
  nextTick(() => {
    loadingStatus.record = false
  })
  if (/\/contentGame\/\d+/.test(route.redirectedFrom?.path)) {
    eventReport({
      event_type: EventTypeEnum.ENTER_STORY_PAGE,
      front_address: ForwardAddressEnum.STORY_CHAT_PAGE_BACK_CHAT
    }).catch((err) => {
      console.warn(err)
    })
  }
  getAgentMessageAndRecord(true)
  userStore.getUserInfo()
}

const likeAgentText = (data: ChatRecordType) => {
  if (loadingStatus.feedback) return
  loadingStatus.feedback = true
  likeOrUnlikeAgentText({
    message_id: data.message_id,
    is_like: data.is_like ? BOOL_NUMBER.NO : BOOL_NUMBER.YES,
    is_bad: data.is_like ? undefined : BOOL_NUMBER.NO
  })
    .then((res) => {
      if (res.code === 200) {
        useModal({
          message: data.is_like ? t('likeCancelled') : t('likeSuccessful')
        })
        if (!data.is_like) {
          data.is_bad = BOOL_NUMBER.NO
        }
        data.is_like = data.is_like ? BOOL_NUMBER.NO : BOOL_NUMBER.YES
      }
    })
    .catch((err) => {
      console.warn(err)
    })
    .finally(() => {
      loadingStatus.feedback = false
    })
}

const unlikeAgentText = (data: ChatRecordType) => {
  if (loadingStatus.feedback) return
  loadingStatus.feedback = true
  likeOrUnlikeAgentText({
    message_id: data.message_id,
    is_bad: data.is_bad ? BOOL_NUMBER.NO : BOOL_NUMBER.YES,
    is_like: data.is_bad ? undefined : BOOL_NUMBER.NO
  })
    .then((res) => {
      if (res.code === 200) {
        useModal({
          message: data.is_bad ? t('dislikeCancelled') : t('dislikeSuccessful')
        })
        if (!data.is_bad) {
          data.is_like = BOOL_NUMBER.NO
        }
        data.is_bad = data.is_bad ? BOOL_NUMBER.NO : BOOL_NUMBER.YES
      }
    })
    .catch((err) => {
      console.warn(err)
    })
    .finally(() => {
      loadingStatus.feedback = false
    })
}

let saveSound = false
const chatQueryCacheList = ref<string[]>([])

const answerTaskAsync = async (responseText: string, tools?: ToolsType[]) => {
  const matches = handleFakeCommentRecord(responseText)
  loadingStatus.textOutputting = true
  // 逐条添加消息并延迟
  for (let i = 0; i < matches.length; i++) {
    // 跳过空消息
    if (!matches[i].trim()) continue

    // 如果不是第一条消息，等待1.5秒
    if (i > 0) {
      const imageURL = getMarkdownImageUrl(matches[i])?.[0]
      await new Promise((resolve) => {
        if (imageURL) {
          console.log('markdownImageUrl', imageURL)
          // 加载图片
          const img = new Image()
          img.src = imageURL
          img.onload = () => {
            // 图片加载完成，可以进行下一步操作
            console.log('图片加载完成')
            // 添加消息到chatRecord
            resolve(void 0)
          }
          img.onerror = () => {
            // 图片加载失败，可以进行下一步操作
            console.log('图片加载失败')
            // 添加消息到chatRecord
            resolve(void 0)
          }
        } else {
          setTimeout(resolve, 1200)
        }
      })
    }

    // 将消息添加到chatRecord
    chatRecord.value.push({
      type: ChatRecordTypeEnum.AI,
      content: matches[i].trim(),
      is_like: BOOL_NUMBER.NO,
      is_bad: BOOL_NUMBER.NO,
      is_phone_call: BOOL_NUMBER.NO,
      message_id: '',
      timestamp: Date.now(),
      replayTime: 0,
      ttsLoading: false,
      ttsPlaying: false,
      audio_length: Number((matches[i].trim().length * 0.15).toFixed(0))
    })
    scrollToBottom(chatRef.value)
  }
  if (tools?.length) {
    chatRecord.value[chatRecord.value.length - 1].is_search = true
    chatRecord.value[chatRecord.value.length - 1].searchContent = tools
  }
  loadingStatus.streaming = false
  loadingStatus.textOutputting = false
  if (chatQueryCacheList.value.length) {
    // 处理所有缓存的消息
    const cachedMessages = chatQueryCacheList.value.join('|')
    chatQueryCacheList.value = []
    blockSendQuestion(cachedMessages)
  }
}

const blockSendQuestion = (text?: string) => {
  // 如果没有传入text且输入框为空，则直接返回
  if (!text && !question.value) return

  const timestamp = Date.now()
  const messageContent = text || question.value

  // 只有当不是处理缓存消息时，才添加到聊天记录
  // 如果是处理缓存消息(text有值)，则不需要添加，因为消息已经在之前添加过了
  if (!text) {
    chatRecord.value.push({
      type: ChatRecordTypeEnum.USER,
      timestamp,
      content: messageContent,
      is_like: BOOL_NUMBER.NO,
      is_bad: BOOL_NUMBER.NO,
      is_phone_call: BOOL_NUMBER.NO,
      message_id: '',
      replayTime: 0
    })
    scrollToBottom(chatRef.value)
  }

  // 如果正在处理其他消息，则缓存当前消息
  if (loadingStatus.streaming) {
    saveSound = !loadingStatus.textOutputting
    if (!text) {
      // 只有新消息才添加到缓存
      chatQueryCacheList.value.push(messageContent)
      question.value = ''
    }
    return
  }

  // 清空输入框
  if (!text) {
    question.value = ''
  }
  loadingStatus.streaming = true
  blockAgentTextChat({
    query: messageContent,
    ai_id: aiID.value
  })
    .then(({ code, data }) => {
      if (code === ResultEnum.SUCCESS) {
        l2dEmo.value = data.pjh_resp.llm_emotion
        handleVoicePlay(data.voice_path)
        answerTaskAsync(data.pjh_resp.frontend_answer, data.pjh_resp.tools)
      }
    })
    .catch((err) => {
      console.warn(err)
      loadingStatus.streaming = false
    })
}

const webSearch = (data: ToolsType[], index: number) => {
  chatRecord.value[index].is_search = false
  loadingStatus.streaming = true
  scrollToBottom(chatRef.value)
  webSearchTool({
    ai_id: aiID.value,
    ...data[0]
  })
    .then(({ code, data }) => {
      if (code === ResultEnum.SUCCESS) {
        console.log(data)
        chatRecord.value.push({
          type: ChatRecordTypeEnum.AI,
          content: data.pjh_resp.frontend_answer.trim(),
          is_like: BOOL_NUMBER.NO,
          is_bad: BOOL_NUMBER.NO,
          is_phone_call: BOOL_NUMBER.NO,
          message_id: '',
          timestamp: Date.now()
        })
        scrollToBottom(chatRef.value)
      }
    })
    .finally(() => {
      loadingStatus.streaming = false
      if (chatQueryCacheList.value.length) {
        // 处理所有缓存的消息
        const cachedMessages = chatQueryCacheList.value.join('|')
        chatQueryCacheList.value = []
        blockSendQuestion(cachedMessages)
      }
    })
}

const debounceSendMessage = debounce(blockSendQuestion, 500, {
  leading: true,
  trailing: false
})

getDress()
permissionInit()

provide('aiID', aiID)
provide('agentMessage', agentMessage)
provide('live2dStatus', live2dStatus)

watch(
  // @ts-ignore
  () => route.params.ai_id,
  (id) => {
    if (id) {
      aiID.value = id as number
    }
  },
  {
    immediate: true,
    deep: true
  }
)

watch(
  () => live2dStatus.value,
  (val) => {
    if (val) {
      eventReport({
        event_type: EventTypeEnum.ENTER_3D_BACKGROUND,
        ai_id: aiID.value
      }).catch((err) => {
        console.warn(err)
      })
      nextTick(() => {
        useIntersectionObserver(
          live2dClear.value,
          ([{ isIntersecting }]) => {
            isClearMode.value = isIntersecting
            if (isIntersecting) {
              eventReport({
                event_type: EventTypeEnum.ENTER_IMMERSE_MODE,
                ai_id: aiID.value
              }).catch((err) => {
                console.warn(err)
              })
              userStore.newerOption.hadSwitchToClearMode = true
              handleGuideTouch()
            }
          },
          {
            threshold: 0.8
          }
        )
      })
    } else {
      l2dRef.value.erosModel?.destroySound()
      eventReport({
        event_type: EventTypeEnum.EXIT_3D_BACKGROUND,
        ai_id: aiID.value
      }).catch((err) => {
        console.warn(err)
      })
    }
  }
)

// 监听页面可见性变化
document.addEventListener('visibilitychange', () => {
  controlBgm()
})

onMounted(() => {
  const observer = new ResizeObserver(() => {
    scrollToBottom(chatRef.value)
  })
  observer.observe(collapseContentRef.value)

  window.addEventListener('resize', () => {
    scrollToBottom(chatRef.value)
  })

  // animationInstance.value = lottie.loadAnimation({
  //   container: heartRef.value, // 容器
  //   renderer: 'svg', // 通过svg或canvas渲染
  //   loop: false, // 是否循环
  //   autoplay: false, // 是否自动播放
  //   animationData: animation, // 动画文件
  //   assetsPath: (import.meta.env.VITE_APP_PUBLIC_PATH === '/' ? '' : import.meta.env.VITE_APP_PUBLIC_PATH) + '/heart-diffuse/' // 动画文件路径
  // })
  // //@ts-ignore
  // animationInstance.value.onComplete = () => {
  //   animationInstance.value.stop()
  // }

  // 无法滚动时 电脑适配滚轮
  chatRef.value.addEventListener('wheel', (event: WheelEvent) => {
    if (event.deltaY > 0) {
      if (chatRef.value.scrollHeight - chatRef.value.clientHeight - chatRef.value.scrollTop <= 4) {
        flagStatus.halfScreen = true
      }
    } else {
      flagStatus.halfScreen = false
    }
  })
  // 无法滚动时适配手机触摸
  chatRef.value.addEventListener('touchstart', (e: TouchEvent) => {
    closeCollapse()
    slidePoint.start = e.touches[0].pageY
  })
  chatRef.value?.addEventListener('scroll', () => {
    flagStatus.showScrollDown = chatRef.value.scrollHeight - chatRef.value.clientHeight - chatRef.value.scrollTop > 120
  })
  chatRef.value.addEventListener('touchmove', (e: TouchEvent) => {
    slidePoint.end = e.touches[0].pageY
    if (slidePoint.end > slidePoint.start) {
      flagStatus.halfScreen = false
    } else if (slidePoint.end < slidePoint.start) {
      if (chatRef.value.scrollHeight - chatRef.value.clientHeight - chatRef.value.scrollTop <= 4) {
        flagStatus.halfScreen = true
      }
    }
  })

  talkBtnRef.value.addEventListener('touchstart', (e: TouchEvent) => {
    console.log(flagStatus.micAllow)
    ugPhoneLinkSdk.micAllow()
    // 禁止穿透
    e.preventDefault()
    if (loadingStatus.tts) {
      useModal({
        message: t('conversationProcessing')
      })
      return
    }
    if (flagStatus.micAllow) {
      navigator?.vibrate?.(100)
      recOpenInit(recStart)
      clickTime.start = Date.now()
      slidePoint.start = e.touches[0].pageY
    } else {
      recOpenInit(null)
    }
  })

  talkBtnRef.value.addEventListener('touchmove', (e: TouchEvent) => {
    // if (loadingStatus.tts || loadingStatus.text) return
    if (flagStatus.micAllow) {
      slidePoint.end = e.touches[0].pageY
    }
  })
  talkBtnRef.value.addEventListener('touchend', (e: TouchEvent) => {
    // if (loadingStatus.tts || loadingStatus.text) return
    if (flagStatus.micAllow) {
      clickTime.end = Date.now()
      slidePoint.end = e.changedTouches[0].pageY
      try {
        // 向下滑动不取消
        if (slidePoint.start - slidePoint.end > 60 && slidePoint.end < slidePoint.start) {
          console.log('取消录音')
          recStop(true)
        } else {
          console.log('正常录音')
          recStop(false)
        }
      } catch (error) {
        console.warn(error)
      }
      countDownRef.value.reset()
      loadingStatus.record = false
      if (clickTime.end - clickTime.start < 500) {
        useModal({
          message: t('voiceTooShort'),
          duration: 1000
        })
      }
    }
    slidePoint.start = 0
    slidePoint.end = 0
  })
})

onBeforeUnmount(() => {
  l2dRef.value?.erosModel?.destroySound?.()
  l2dRef.value?.erosModel?.stopMotionSync?.()
  controlBgm(false, true)
  if (myStream.value) {
    myStream.value.stop()
    myStream.value = null
  }
})
</script>

<template>
  <div
    id="modelContainer"
    class="whole-chat-container"
    :style="
      agentMessage.bg_info?.bg_url &&
      agentMessage.bg_info.type !== SkinTypeEnum.ThreeD && {
        background: `url('${agentMessage.bg_info?.bg_url}') center / cover no-repeat`
      }
    "
  >
    <div class="top-bar">
      <div
        v-show="!isClearMode"
        class="left-content flex-center-center cg-8"
      >
        <SvgIcon
          icon-class="left-arrow"
          class="fsize-24"
          @click="goBack"
        />
        <div class="head-message">
          <div
            class="avatar-container"
            :style="{ '--percentage': '100%' }"
          >
            <div
              ref="heartRef"
              class="heart-down"
            ></div>
            <div
              class="avatar"
              :style="{ backgroundImage: `url('${agentMessage.avatar_url}')` }"
            ></div>
          </div>
          <div
            class="name"
            @click="goHomePage"
          >
            {{ agentMessage.name }} <SvgIcon icon-class="arrow-right-jump" />
          </div>
        </div>
      </div>
      <div
        class="right-btn"
        v-if="!isClearMode"
      >
        <div
          class="switch-btn flex-center-center"
          @click="debounceSetBgmStatus"
        >
          <SvgIcon
            v-show="userStore.userInformation.bgm"
            icon-class="music-on"
            class="fsize-24"
          />
          <SvgIcon
            v-show="!userStore.userInformation.bgm"
            icon-class="music-off"
            class="fsize-24"
          />
        </div>
        <!--        <div-->
        <!--          class="switch-btn flex-center-center"-->
        <!--          v-show="l2dSkinData?.id"-->
        <!--        >-->
        <!--          <SvgIcon-->
        <!--            v-show="!isClearMode"-->
        <!--            icon-class="l2d-change"-->
        <!--            class="fsize-24"-->
        <!--          />-->
        <!--        </div>-->
      </div>
      <div
        v-show="isClearMode"
        @click="exitClearMode"
        class="exit-l2d"
      >
        <van-icon
          name="arrow-left"
          class="mr-4"
        />{{ t('exitClearMode') }}
      </div>
    </div>
    <div
      class="chat-page"
      ref="chatPageRef"
    >
      <div
        ref="chatRef"
        class="chat"
        :style="{
          '--chat-mask-start': flagStatus.halfScreen ? '50%' : '50px',
          '--chat-mask-end': flagStatus.halfScreen ? '55%' : '75px'
        }"
      >
        <div
          class="whole-chat-item"
          v-for="(item, index) in chatRecord"
          :key="item.message_id + item.type + String(index)"
        >
          <div
            ref="replayItemRef"
            class="chat-item-container"
          >
            <!--            <div-->
            <!--              class="sound"-->
            <!--              v-if="item.type === ChatRecordTypeEnum.AI && item.audio_length"-->
            <!--              @click="debounceHandleTTSCase(item)"-->
            <!--            >-->
            <!--              <SoundLoading-->
            <!--                v-if="item.ttsPlaying"-->
            <!--                class="h-full"-->
            <!--              />-->
            <!--              <img-->
            <!--                v-else-if="item.ttsLoading"-->
            <!--                class="sound-loading"-->
            <!--                src="@/assets/images/sound-loading.png"-->
            <!--                alt="loading"-->
            <!--              />-->
            <!--              <SvgIcon-->
            <!--                v-else-->
            <!--                icon-class="play"-->
            <!--                class="fsize-14"-->
            <!--                style="color: #fff"-->
            <!--              />-->
            <!--              <div class="text">{{ item.audio_length.toFixed(0) }}”</div>-->
            <!--            </div>-->
            <div class="chat-item">
              <div
                v-if="item.type === ChatRecordTypeEnum.WARN"
                :class="CHAT_BOX_STYLE_CLASS[item.type]"
              >
                <div class="tips">{{ t('aiContentDisclaimer') }}</div>
              </div>
              <div
                v-else-if="item.type === ChatRecordTypeEnum.SYNOPSIS"
                :class="CHAT_BOX_STYLE_CLASS[item.type]"
              >
                <div class="synopsis">
                  <SvgIcon
                    icon-class="opening"
                    class="fsize-18"
                  />
                  <div>{{ item.content }}</div>
                </div>
              </div>
              <div
                v-else-if="item.type === ChatRecordTypeEnum.PHONE"
                :class="CHAT_BOX_STYLE_CLASS[item.type]"
              >
                <div
                  class="line"
                  style="height: 1px"
                ></div>
                <div class="content">{{ item.content }}</div>
                <div
                  class="line"
                  style="height: 1px"
                ></div>
              </div>
              <div
                v-else-if="item.type === ChatRecordTypeEnum.TIPS"
                :class="CHAT_BOX_STYLE_CLASS[item.type]"
              >
                <div
                  class="line"
                  style="height: 1px"
                ></div>
                <div class="content">{{ item.content }}</div>
                <div
                  class="line"
                  style="height: 1px"
                ></div>
              </div>
              <div
                v-else-if="item.type === ChatRecordTypeEnum.GIFT"
                :class="CHAT_BOX_STYLE_CLASS[item.type]"
              >
                <div
                  class="line"
                  style="height: 1px"
                ></div>
                <div class="content flex-center-center">
                  {{ item.content }}
                  <SvgIcon
                    icon-class="intimacy-value-heart"
                    class="fsize-12"
                  />+{{ item.gift_info?.intimacy }}
                </div>
                <div
                  class="line"
                  style="height: 1px"
                ></div>
              </div>
              <div
                :class="CHAT_BOX_STYLE_CLASS[item.type]"
                v-else
              >
                <div
                  v-on-long-press.prevent="() => onLongPressCallbackDirective(item, index)"
                  class="chat-record"
                >
                  <ChatLoading
                    v-if="item.userTTS"
                    :style="item.type === ChatRecordTypeEnum.USER && { '--uib-color': 'white' }"
                  />
                  <van-popover
                    v-model:show="showPopover[index]"
                    teleport="#app"
                    trigger="manual"
                    :placement="dividePlacement(item.type)"
                  >
                    <div class="flex-center-center pb-14 pt-14 pl-14 pr-14 cg-44">
                      <div
                        class="flex-center-center flex-column fsize-12 rg-8"
                        @click="likeAgentText(item)"
                      >
                        <SvgIcon
                          :icon-class="item.is_like ? 'like-hand-active' : 'like-hand'"
                          class="fsize-20"
                        />{{ t('like') }}
                      </div>
                      <div
                        class="flex-center-center flex-column fsize-12 rg-8"
                        @click="unlikeAgentText(item)"
                      >
                        <SvgIcon
                          :icon-class="item.is_bad ? 'unlike-hand-active' : 'unlike-hand'"
                          class="fsize-20"
                        />{{ t('dislike') }}
                      </div>
                      <div
                        class="flex-center-center flex-column fsize-12 rg-8"
                        @click="copyText(item.content)"
                      >
                        <SvgIcon
                          icon-class="copy-option"
                          class="fsize-20"
                        />{{ t('copy') }}
                      </div>
                    </div>

                    <template #reference>
                      <div
                        class="no-selected"
                        v-html="markdownToHtml(replaceBracketsWithAsterisks(item.content))"
                      ></div>
                    </template>
                  </van-popover>
                </div>
              </div>
              <div
                v-if="item.is_search && index === chatRecord.length - 1"
                class="web-search"
                @click="webSearch(item.searchContent, index)"
              >
                <SvgIcon icon-class="search" />
                {{ t('search') }}
              </div>
            </div>
          </div>
        </div>
        <div
          class="whole-chat-item"
          v-if="loadingStatus.streaming"
        >
          <div :class="CHAT_BOX_STYLE_CLASS[ChatRecordTypeEnum.AI]">
            <div class="chat-record">
              <ChatLoading />
            </div>
          </div>
        </div>
      </div>
      <div class="accordion">
        <div
          class="scroll-top flex-center-center"
          v-show="flagStatus.showScrollDown"
          @click="scrollToBottom(chatRef, 'smooth', false)"
        >
          <SvgIcon
            icon-class="scroll-arrow"
            class="fsize-24"
          />
        </div>
        <div
          class="talk-mask"
          v-if="loadingStatus.record"
        >
          <div
            class="mask-text"
            :style="isCancelDistance ? 'color: #F62A5A' : 'color: #fff'"
          >
            {{ isCancelDistance ? t('releaseToCancelSend') : t('releaseToSendSwipeUpCancel') }}
          </div>
        </div>
        <div class="input-container">
          <SvgIcon
            @click="changeInputting"
            :icon-class="flagStatus.inputting ? 'microphone' : 'keyboard'"
            class="fsize-28"
          />
          <van-field
            type="textarea"
            v-show="flagStatus.inputting"
            class="flex-1 input"
            v-model="question"
            @focus="closeCollapse"
            :autosize="{
              maxHeight: 100
            }"
            rows="1"
            @keydown.enter.prevent="debounceSendMessage()"
          />
          <div
            v-show="!flagStatus.inputting"
            ref="talkBtnRef"
            :style="loadingStatus.record && (isCancelDistance ? 'background: #FF5B4D' : 'background: #EBDFAC')"
            class="flex-1 talk-btn flex-center-center"
          >
            <div v-show="!loadingStatus.record">{{ t('holdToSpeak') }}</div>
            <div
              class="wave"
              v-show="loadingStatus.record"
            ></div>
            <van-count-down
              v-show="loadingStatus.record"
              ref="countDownRef"
              millisecond
              :time="60000"
              :auto-start="false"
              @finish="countDownFinish"
            >
              <template #default="timeData">
                <span
                  class="block"
                  :style="isCancelDistance ? 'color: #fff' : 'color: #2f2101'"
                  >{{ timeData.seconds }}s
                </span>
              </template>
            </van-count-down>
          </div>
          <SvgIcon
            v-if="question"
            icon-class="send"
            class="fsize-36"
            @click="handleCollapse(Boolean(question))"
          />
          <SvgIcon
            v-else
            :icon-class="isCollapse ? 'cross-button' : 'add-plus-button'"
            class="fsize-28 switch-mode"
            @click="handleCollapse(Boolean(question))"
          />
        </div>
        <div
          ref="collapseContentRef"
          class="collapse-content"
        >
          <div
            class="collapse-item"
            :style="!agentMessage.ai_chat_setting?.intimacy_interest_tags?.includes('phone_call') && { opacity: 0.5 }"
            @click="enterPhoneCall"
          >
            <SvgIcon
              icon-class="phone"
              class="fsize-32"
            />
            <div class="fsize-12">{{ t('makeCall') }}</div>
          </div>
          <div
            class="collapse-item"
            @click="restartChat"
          >
            <SvgIcon
              icon-class="agent-restart"
              class="fsize-32"
            />
            <div class="fsize-12">{{ t('startNewChat') }}</div>
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="live2dStatus"
      ref="live2dClear"
      class="live2d-clear"
    ></div>
    <RecordPermissionDialog
      v-model="permissionVisible"
      @get-permission="phoneCallPermission"
    />
    <KeepAlive>
      <Live2D
        ref="l2dRef"
        v-if="flagStatus.live2d"
        :emo="l2dEmo"
        :style="!live2dStatus && { opacity: 0 }"
        :is-clear-mode="isClearMode"
        :asset-url="live2dResource.model_json"
        :change-url="live2dResource.change_model_json"
        :action-list="live2dResource.action_list"
        :back-pic="live2dResource.back_pic"
        :params="{
          scale: live2dResource.scale,
          rotate: live2dResource.rotate / 360,
          anchor: {
            x: live2dResource.vertical,
            y: live2dResource.horizontal
          }
        }"
      />
    </KeepAlive>
    <PhoneCall
      :key="Date.now()"
      v-model="flagStatus.phoneCall"
      v-if="flagStatus.phoneCall"
      :audio-context="runningContextAudioForPhone"
      @get-record="permissionInit"
    />
    <CallTimeTopUpPopup v-model="topUpVisible" />
  </div>
</template>
<style>
p {
  margin: 0;
}

em {
  margin-right: 4px;
  opacity: 0.5;
}

code {
  white-space: pre-wrap;
}

.markdown-image {
  display: none;
}
</style>
<style scoped lang="scss">
@import 'src/assets/styles/chatbox';

@property --chat-mask-start {
  syntax: '<length-percentage>';
  inherits: false;
  initial-value: 50px;
}

@property --chat-mask-end {
  syntax: '<length-percentage>';
  inherits: false;
  initial-value: 75px;
}

.no-selected {
  pointer-events: none;
}

.whole-chat-container {
  display: flex;
  width: 100%;
  height: 100%;
  overflow-x: auto;
  overscroll-behavior: none;
  background: url('/ugenie.png') center / 50% no-repeat;
  scroll-snap-type: x mandatory;

  &::-webkit-scrollbar {
    display: none;
  }

  .top-bar {
    position: fixed;
    top: 0;
    z-index: 3;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 60px;
    padding: 6px 12px 14px 6px;
    overscroll-behavior: none;
    background: linear-gradient(180deg, rgba(24, 24, 24, 50%) 0%, rgba(24, 24, 24, 0%) 100%);

    .right-btn {
      display: flex;
      column-gap: 10px;
      align-items: center;
      justify-content: center;
    }

    .switch-btn {
      width: 38px;
      height: 38px;
      background: rgba(24, 24, 24, 60%);
      backdrop-filter: blur(16px);
      border-radius: 12px;
    }

    .head-message {
      position: relative;
      display: flex;
      column-gap: 8px;
      align-items: center;
      justify-content: center;
      background: rgba(24, 24, 24, 60%);
      backdrop-filter: blur(16px);
      border-radius: 28px;

      .heart-down {
        position: absolute;
        bottom: 0;
        left: 50%;
        z-index: 4;
        display: flex;
        align-items: center;
        justify-content: center;
        //width: 36px;
        //height: 56px;
        width: 16px;
        height: 16px;
        font-size: 12px;
        font-weight: bold;
        transform: translate(-50%, 40%);

        .num {
          position: absolute;
          bottom: 0;
          z-index: 4;
          font-size: 10px;
          color: $livCoTextColor;
          transform: translateY(-20%);
        }
      }

      .avatar-container {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        margin: 3px;
        background: conic-gradient(from -180deg, $livCoThemeColor var(--percentage), rgba(235, 223, 172, 30%) var(--percentage) 100%);
        border-radius: 50%;

        .avatar {
          width: 28px;
          height: 28px;
          background: url('@/assets/images/normal-holder.png') no-repeat center / cover;
          border-radius: 50%;
        }
      }

      .name {
        display: flex;
        align-items: center;
        margin-right: 8px;
        font-family: 'Heiti SC', Roboto, serif;
        font-size: 14px;
      }
    }

    .exit-l2d {
      padding: 10px 16px 10px 10px;
      margin-left: 10px;
      font-size: 14px;
      background: rgba(24, 24, 24, 40%);
      backdrop-filter: blur(4px);
      border: 1px solid rgba(255, 255, 255, 20%);
      border-radius: 24px;
    }
  }

  .chat-page {
    z-index: 2;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    align-items: flex-end;
    width: 100%;
    height: 100%;
    overscroll-behavior: none;
  }

  .chat {
    position: relative;
    z-index: 1;
    display: flex;
    flex: 1;
    flex-direction: column;
    row-gap: 11px;
    width: 100%;
    height: 100%;
    padding: 64px 12px 20px;
    overflow: auto;
    transition:
      --chat-mask-start 0.2s linear,
      --chat-mask-end 0.2s linear;
    scroll-snap-align: start;
    mask-image: linear-gradient(
      to bottom,
      rgba(0, 0, 0, 0%) var(--chat-mask-start),
      rgba(0, 0, 0, 100%) var(--chat-mask-end),
      rgba(0, 0, 0, 100%) calc(100% - 20px),
      rgba(0, 0, 0, 0%) 100%
    );

    .half-screen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 50%;
      background: transparent;
    }

    :deep(.van-popover__wrapper) {
      display: block;
    }

    .whole-chat-item {
      .down-option {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 8px;

        .pardon {
          padding: 6px 12px;
          background: rgba(24, 24, 24, 60%);
          border-radius: 12px;
        }

        .replay-change {
          display: flex;
          column-gap: 12px;
          align-items: center;

          .arrow {
            padding: 6px 8px;
            background: rgba(24, 24, 24, 60%);
            border-radius: 12px;
          }
        }
      }
    }

    .chat-item-container {
      .sound {
        position: absolute;
        top: 0;
        left: 4px;
        z-index: 1;
        display: flex;
        column-gap: 2px;
        align-items: center;
        justify-content: center;
        height: 22px;
        padding: 4px 8px;
        background: #b8aa6e;
        border-radius: 16px 16px 16px 8px;

        :deep(.loading-bar) {
          background: #fff;
        }

        .text {
          font-size: 12px;
          color: #fff;
        }

        .sound-loading {
          width: 14px;
          height: 14px;
          color: #fff;
          animation: loading 1s linear infinite;
        }

        @keyframes loading {
          0% {
            transform: rotate(0deg);
          }

          100% {
            transform: rotate(360deg);
          }
        }
      }
    }
  }

  .undress-gift {
    z-index: 3;
    display: flex;
    column-gap: 8px;
    align-items: center;
    width: 100%;
    padding: 0 0 8px;

    .undress {
      display: flex;
      column-gap: 8px;
      align-items: center;
      justify-content: center;
      padding: 7px 12px;
      font-weight: 400;
      background: transparent;
      border: 1px solid rgba(255, 255, 255, 20%);
      border-radius: 12px;
    }

    .gift {
      display: flex;
      column-gap: 8px;
      align-items: center;
      justify-content: center;
      padding: 3px;
      background: transparent;
      border: 1px solid rgba(255, 255, 255, 20%);
      border-radius: 12px;

      img {
        width: 24px;
        height: 24px;
      }
    }

    .content-game-list {
      display: flex;
      flex: 1;
      column-gap: 8px;
      height: fit-content;
      padding: 0;
      overflow: auto;

      &::-webkit-scrollbar {
        display: none;
      }

      .content-game {
        display: flex;
        flex-shrink: 0;
        column-gap: 2px;
        align-items: center;
        justify-content: center;
        padding: 8px;
        font-size: 12px;
        font-weight: 400;
        line-height: 12px;
        color: #fff;
        text-align: left;
        background: #ffffff14;
        border-radius: 11px;
      }
    }
  }

  .accordion {
    position: relative;
    z-index: 4;
    width: 100%;
    padding: 12px 0 24px 12px;
    background: rgba(24, 24, 24, 40%);
    backdrop-filter: blur(32px);
    border-radius: 8px 8px 0 0;

    .scroll-top {
      position: absolute;
      top: -58px;
      right: 12px;
      width: 42px;
      height: 42px;
      background: rgba(62, 62, 62, 70%);
      backdrop-filter: blur(64px);
      border: 1px solid rgba(255, 255, 255, 10%);
      border-radius: 52px;
    }

    .talk-mask {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 208px;
      pointer-events: none;
      background: linear-gradient(360deg, #000 0%, #000 55%, rgba(0, 0, 0, 0%) 100%);

      .mask-text {
        position: absolute;
        bottom: 84px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        font-size: 12px;
      }
    }

    .input-container {
      display: flex;
      column-gap: 12px;
      align-items: center;
      width: 100%;
      padding-right: 12px;

      :deep(.van-field) {
        min-height: 42px;
        padding-top: 4px;
        padding-bottom: 4px;

        &::after {
          display: none;
        }
      }
    }

    .input {
      background: rgba(255, 255, 255, 15%);
      border: 1px solid rgba(223, 219, 255, 20%);
      border-radius: 16px;
    }

    .talk-btn {
      z-index: 3;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 42px;
      padding: 0 12px;
      user-select: none;
      background: rgba(133, 120, 137, 50%);
      border: 1px solid rgba(223, 219, 255, 20%);
      border-radius: 16px;

      .wave {
        flex: 1;
        height: 100%;
      }
    }

    .collapse-content {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      row-gap: 18px;
      width: 100%;
      max-height: 0;
      padding-right: 12px;
      overflow: hidden;
      transition: max-height 0.2s ease-out;

      .collapse-item {
        position: relative;
        display: flex;
        flex-direction: column;
        row-gap: 12px;
        align-items: center;
        justify-content: flex-start;
        min-height: 78px;
        padding: 8px;
        overflow: hidden;

        svg {
          flex-shrink: 0;
        }

        div {
          flex-shrink: 0;
          text-align: center;
        }

        .used-act {
          position: absolute;
          top: 0;
          left: 0;
          padding: 4px 6px;
          font-size: 8px;
          background: linear-gradient(140deg, #ff35f2 0%, #98f 100%);
          border-radius: 12px 12px 12px 4px;
        }
      }
    }
  }

  .content-game-btn {
    z-index: 3;
    width: 100%;
    padding: 12px 16px;
    background: rgba(24, 24, 24, 40%);
    backdrop-filter: blur(88px);
    border-radius: 12px 12px 0 0;

    .go {
      display: flex;
      column-gap: 4px;
      align-items: center;
      justify-content: center;
      padding: 16px 0;
      margin-bottom: 12px;
      font-size: 14px;
      font-weight: 600;
      line-height: 16px;
      color: #fff;
      text-align: center;
      background: rgba(255, 255, 255, 15%);
      border-radius: 16px;
    }

    .later {
      padding: 16px 0;
      font-size: 14px;
      font-weight: 600;
      line-height: 16px;
      color: #fff;
      text-align: center;
      background: rgba(255, 255, 255, 15%);
      border-radius: 16px;
    }
  }

  .live2d-clear {
    scroll-snap-align: start;
    z-index: 2;
    display: flex;
    flex-shrink: 0;
    align-items: flex-end;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }
}
</style>
