<script lang="ts" setup>
import { getRankingList } from '@/api/ranking'
import { IRankingParams, IRankingItem } from '@/api/ranking/types'
import useAppStore from '@/stores/modules/app'
import useUserStore from '@/stores/modules/user'
import { eventReport, EventTypeEnum, ForwardAddressEnum } from '@/api/eventReport'

const props = withDefaults(
  defineProps<{
    type: number
  }>(),
  {
    type: 0
  }
)
const userStore = useUserStore()
const appStore = useAppStore()
const router = useRouter()
const rankingList = ref<IRankingItem[]>([])
const loading = ref(false)
const finished = ref(false)
const refreshing = ref(false)
const rankParams = ref<IRankingParams>({
  page: 1,
  limit: 10,
  sort_type: props.type
})
const reportTypeList = [ForwardAddressEnum.POPULARITY_CHART, ForwardAddressEnum.FLOWER_LIST]
function navToChat(ai_id: number) {
  if (!userStore.token) return (appStore.showLogin = true)
  eventReport({
    event_type: EventTypeEnum.ENTER_CHAT_SCREEN,
    front_address: reportTypeList[rankParams.value.sort_type - 1],
    ai_id: ai_id
  }).catch((err) => {
    console.warn(err)
  })
  router.push(`/chat/${ai_id}`)
}

function getHotSearchHandle() {
  loading.value = true
  getRankingList(rankParams.value)
    .then((res) => {
      if (res.code !== 200) return
      if (!res.data.list) return (finished.value = true)
      rankingList.value.push(...res.data.list)
      rankParams.value.page++
      if (rankingList.value.length >= res.data.count) {
        finished.value = true
      }
    })
    .catch(() => {
      loading.value = false
      refreshing.value = false
      finished.value = true
    })
    .finally(() => {
      loading.value = false
      refreshing.value = false
    })
}
const onLoad = () => {
  if (refreshing.value) {
    rankingList.value = []
    loading.value = true
    refreshing.value = false
  }
  getHotSearchHandle()
}

const onRefresh = () => {
  // 清空列表数据
  finished.value = false
  loading.value = true
  onLoad()
}

const refresh = () => {
  rankParams.value.sort_type = props.type
  rankParams.value.page = 1
  rankingList.value = []
  finished.value = false
  getHotSearchHandle()
}
watch(
  () => props.type,
  () => {
    refresh()
  }
)

console.log(localStorage.getItem('language'))

defineExpose({
  loading,
  refresh
})
</script>

<template>
  <div class="rank-container">
    <van-pull-refresh
      :pulling-text="$t('pullToRefresh')"
      :loading-text="$t('loading')"
      :loosing-text="$t('looseToRefresh')"
      :disabled="true"
      v-model="refreshing"
      @refresh="onRefresh"
    >
      <van-list
        v-model:loading="loading"
        :finished="finished"
        :loading-text="$t('loading')"
        :finished-text="''"
        @load="onLoad"
      >
        <template #loading>
          <img
            class="loading-icon"
            src="@/assets/images/index/loading-icon.png"
            alt="loading"
          />
        </template>
        <div
          class="out-wrap"
          :class="index < 3 ? `out-wrap-active${index + 1}` : ''"
          v-for="(item, index) in rankingList"
          :key="item.id"
        >
          <div
            class="hot-role-item flex-between-center"
            :class="index < 3 ? `role-active${index + 1}` : ''"
            @click="navToChat(item.id)"
          >
            <van-image
              class="hot-role-img"
              :src="item.avatar_url"
              alt="hot-role-img"
              fit="cover"
              position="top"
            />
            <div class="hot-role-content">
              <div class="hot-role-content__title">{{ item.name }}</div>
              <div class="hot-role-content__text">{{ item.synopsis }}</div>
              <div class="hot-role-content__info flex-between-center">
                <div class="info-author"></div>
                <div class="info-popularity flex-between-center">
                  <div
                    class="view flex-between-center"
                    v-if="props.type === 1"
                  >
                    <SvgIcon
                      icon-class="chat-times"
                      class="fsize-14 ml-6"
                    />
                    <div class="ml-2">{{ item.chat_times }}</div>
                  </div>
                  <div
                    class="love flex-between-center"
                    v-if="props.type === 2"
                  >
                    <SvgIcon
                      :icon-class="'gift-icon'"
                      class="fsize-14 ml-6"
                    />
                    <div class="ml-2">{{ item.received_gift_count }}</div>
                  </div>
                </div>
              </div>
            </div>
            <div :class="['hot-role-subscript flex-center-center', index < 3 ? `active${index + 1} br0` : '']">
              {{ index < 3 ? '' : index + 1 }}
            </div>
          </div>
        </div>
      </van-list>
    </van-pull-refresh>
  </div>
</template>

<style lang="scss" scoped>
@import '@/assets/styles/mixin';

:deep(.van-list) {
  height: 100%;
}

:deep(.van-image__error) {
  border-radius: 8px;
}

.rank-container {
  height: calc(100% - 168px);
  padding: 0 16px;
  overflow-y: scroll;

  .info-author {
    display: flex;
    align-items: center;
    line-height: 14px;
    vertical-align: middle;
  }

  .out-wrap {
    margin-bottom: 12px;
    background-clip: padding-box, border-box;
    background-origin: padding-box, border-box;
    border: 1px solid transparent;
    border-radius: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .out-wrap-active1 {
    background-image: linear-gradient(to right, #1a1a1a, #1a1a1a), linear-gradient(90deg, rgba(255, 189, 90, 0%), rgba(255, 189, 90, 50%));
  }

  .out-wrap-active2 {
    background-image: linear-gradient(to right, #1a1a1a, #1a1a1a), linear-gradient(90deg, rgba(146, 187, 250, 0%), rgba(146, 187, 250, 30%));
  }

  .out-wrap-active3 {
    background-image: linear-gradient(to right, #1a1a1a, #1a1a1a), linear-gradient(90deg, rgba(255, 152, 118, 0%), rgba(255, 152, 118, 30%));
  }

  .hot-role-item {
    position: relative;
    max-height: 100px;
    padding: 12px;
    background: #232222;
    border-radius: 16px;

    :deep(.van-image) {
      img {
        border-radius: 8px;
      }
    }
  }

  .role-active1 {
    background: #2c261e;
  }

  .role-active2 {
    background: #24272c;
  }

  .role-active3 {
    background: #2c2421;
  }

  .hot-role-img {
    width: 68px;
    height: 68px;
    margin-right: 12px;
    vertical-align: middle;
    border-radius: 8px;
  }

  .hot-role-content {
    display: flex;
    flex: 1;
    flex-direction: column;
    height: 68px;
  }

  .hot-role-content__title {
    font-weight: 500;
    line-height: 16px;
  }

  .hot-role-content__text {
    flex: 1;
    margin: 4px 0 8px;
    font-size: 12px;
    line-height: 14px;
    color: rgba(255, 255, 255, 60%);

    @include multiLine(2);
  }

  .hot-role-content__info {
    font-size: 12px;
    color: rgba(255, 255, 255, 50%);
  }

  .hot-role-subscript {
    position: absolute;
    top: 0;
    left: 0;
    min-width: 20px;
    min-height: 24px;
    padding: 4px 8px;
    font-size: 14px;
    font-weight: 900;
    color: #cbcbcb;
    background: #363535;
    border-radius: 12px 0;

    &.active1 {
      background: url('~/images/search/hot-icon-one.png') center / contain no-repeat;
    }

    &.active2 {
      background: url('~/images/search/hot-icon-two.png') center / contain no-repeat;
    }

    &.active3 {
      background: url('~/images/search/hot-icon-three.png') center / contain no-repeat;
    }

    &.br0 {
      border-radius: 0;
    }
  }
}

.loading-icon {
  width: 24px;
  height: 24px;
  animation: loading 1s linear infinite;
}

@keyframes loading {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
