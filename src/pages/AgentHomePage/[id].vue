<script setup lang="ts">
import { getAIInfo } from '@/api/agentHomePage'
import { AgentMessageType } from '@/api/agentHomePage/types.ts'
import { copyText } from '@/utils'
// import { useModal } from '@/hooks/useModal.ts'
import { eventReport, EventTypeEnum, ForwardAddressEnum } from '@/api/eventReport'
import { useBackHistory } from '@/hooks/useCommon.ts'
import useAppStore from '@/stores/modules/app.ts'

const { t } = useI18n()

const { share } = useShare()
const scrollContainerRef = ref()
const activeName = ref('a')
const appStore = useAppStore()
const router = useRouter()
const route = useRoute()
const shareVisible = ref(false)
const sexEnum: Record<number, string> = {
  1: 'man',
  2: 'woman',
  3: 'no-sex'
}

const aiID = computed(() => {
  if ('id' in route.params) {
    return Number(route.params.id)
  }
})

const optionVisible = ref(false)
const albumVisible = ref(false)
const storyVisible = ref(false)

const agentMessage = ref<AgentMessageType>({
  tag_list: [],
  like_status: 0,
  ai_chat_setting: {
    image_id: undefined,
    show_disclaimer: 0,
    show_character: 0,
    show_prologue: 0,
    show_muse: 0,
    intimacy_now: 0,
    intimacy_upgrade: 0,
    intimacy_level: 0,
    intimacy_interest_tags: []
  },
  opening_option: [],
  id: undefined,
  has_3d: undefined,
  is_repeat: undefined,
  last_set_id: undefined,
  name: undefined,
  follow: '0K',
  chat_times: '0K',
  image_url: undefined,
  init_plot: undefined,
  update_time: undefined,
  sound_ray_url: undefined,
  opening_statement_voice: undefined,
  intimacy_level_info: [],
  description: undefined,
  api_key: undefined,
  camera_pic_status: undefined,
  dataset_id: undefined,
  app_id: undefined,
  sex: undefined,
  role_setting: undefined,
  opening_statement: undefined,
  back_story: undefined,
  talk_example: undefined,
  synopsis: undefined,
  voice_id: undefined,
  is_show: 0,
  is_quick: 0,
  status: 0,
  avatar_url: undefined,
  client_image_id: undefined,
  terminal: undefined,
  bgm: '',
  bg_info: {
    bg_url: undefined,
    set_id: undefined,
    dress_id: undefined,
    type: undefined
  },
  client_info: {
    client_id: undefined,
    name: undefined,
    email: undefined,
    avatar_url: undefined
  },
  admin_info: {
    admin_id: undefined,
    email: undefined,
    name: undefined,
    avatar_url: undefined
  },
  resident_function: [],
  resident_type: 3
})

const shareFunc = () => {
  if (appStore.isAndroid) {
    shareVisible.value = true
  } else {
    share({
      title: t('shareContent'),
      text: 'Eros',
      url: window.location.href
    })
  }
  eventReport({
    event_type: EventTypeEnum.CLICK_SHARE,
    ai_id: aiID.value
  }).catch((err) => {
    console.warn(err)
  })
}

const getAgentMessage = () => {
  if (route.query.tab) {
    activeName.value = route.query.tab as string
    console.log(route)
    eventReport({
      event_type: EventTypeEnum.ENTER_STORY_PAGE,
      front_address: route.redirectedFrom?.path
    }).catch((err) => {
      console.warn(err)
    })
  }
  getAIInfo({ ai_id: aiID.value }).then((res) => {
    const { code, data } = res
    if (code === 200) {
      console.log(data)
      agentMessage.value = data
    }
  })
}

const changeTab = (val: string) => {
  if (val === 'd') {
    eventReport({
      event_type: EventTypeEnum.ENTER_STORY_PAGE,
      front_address: route.path
    }).catch((err) => {
      console.warn(err)
    })
  }
}

const goChat = () => {
  eventReport({
    event_type: EventTypeEnum.ENTER_CHAT_SCREEN,
    front_address: ForwardAddressEnum.AI_PROFILE,
    ai_id: aiID.value
  }).catch((err) => {
    console.warn(err)
  })
  router.push(`/chat/${aiID.value}`)
}

// const setLike = () => {
//   const { close } = useModal({
//     loading: true,
//     message: agentMessage.value.like_status ? t('unfollowOption') : t('followOption'),
//     autoClose: false
//   })
//   setAgentLike({ ai_id: aiID.value })
//     .then((res) => {
//       const { code, data } = res
//       if (code === 200) {
//         agentMessage.value.like_status = agentMessage.value.like_status ? 0 : 1
//         agentMessage.value.follow = data
//       }
//     })
//     .catch((err) => {
//       console.warn(err)
//     })
//     .finally(() => {
//       close()
//     })
// }

getAgentMessage()

provide('agentMessage', agentMessage)
provide('activeName', activeName)
</script>

<template>
  <div
    class="back-image"
    :style="{ backgroundImage: `url('${agentMessage?.image_url}')` }"
  ></div>
  <div
    class="top-bar flex-between-center"
    v-scroll-gradation-bg="{ scrollElement: scrollContainerRef, completedHeight: 250, startHeight: 200 }"
  >
    <SvgIcon
      icon-class="left-arrow"
      @click="useBackHistory"
    />
    <div class="right-svg">
      <SvgIcon
        icon-class="share"
        @click="shareFunc"
        class="mr-16"
      />
      <SvgIcon
        icon-class="option"
        @click="optionVisible = true"
      />
    </div>
  </div>
  <div
    class="scroll-content"
    ref="scrollContainerRef"
  >
    <div class="content">
      <div class="box"></div>
      <div class="title-container flex-between-end">
        <div class="message">
          <div class="agent-name flex-start-center cg-8">
            {{ agentMessage.name }}
            <SvgIcon :icon-class="sexEnum[agentMessage.sex] || 'woman'" />
          </div>
          <div class="author">
            <!--            <img-->
            <!--              :src="agentMessage.client_info?.avatar_url || agentMessage.admin_info?.avatar_url"-->
            <!--              alt=""-->
            <!--            />-->
            <!--            <div-->
            <!--              class="name"-->
            <!--              @click="router.push(`/mine/others?id=${agentMessage.client_info?.client_id || agentMessage.admin_info?.admin_id}`)"-->
            <!--            >-->
            <!--              {{ agentMessage.client_info?.name || agentMessage.admin_info?.name }}-->
            <!--            </div>-->
            <div class="agent-id">
              ID: {{ aiID }}
              <SvgIcon
                icon-class="copy"
                class="ml-4"
                @click="copyText(aiID)"
              />
            </div>
          </div>
          <div class="flag">
            <SvgIcon
              icon-class="3d-flag"
              v-show="agentMessage.has_3d"
              class="fsize-24"
            />
            <img
              src="@/assets/images/agentChat/original.png"
              alt="original"
              v-show="agentMessage.is_repeat"
            />
          </div>
        </div>
        <div class="like flex-center-center cg-16">
          <div class="comment-num box">
            <SvgIcon
              icon-class="comment"
              class="svg-size"
            />
            {{ agentMessage.chat_times }}
          </div>
          <!--          <div-->
          <!--            class="like-num box"-->
          <!--            @click="setLike"-->
          <!--          >-->
          <!--            <SvgIcon-->
          <!--              :icon-class="agentMessage.like_status ? 'like-active' : 'like'"-->
          <!--              class="svg-size"-->
          <!--            />{{ agentMessage.follow }}-->
          <!--          </div>-->
        </div>
      </div>
      <div class="tab-container">
        <van-tabs
          v-model:active="activeName"
          line-width="50"
          line-height="2"
          shrink
          animated
          @change="changeTab"
        >
          <van-tab
            :title="t('agentProfile')"
            name="a"
          />
          <template v-if="!appStore.isAndroid">
            <van-tab
              v-if="albumVisible"
              :title="t('photoAlbum')"
              name="b"
            />
            <van-tab
              :title="t('outfits')"
              name="c"
            />
            <van-tab
              v-if="storyVisible"
              :title="t('story')"
              name="d"
            />
          </template>
        </van-tabs>
      </div>
      <div class="tab-content">
        <AboutTA
          v-show="activeName === 'a'"
          class="pl-16 pr-16"
        />
        <PhotoAlbum
          v-model="albumVisible"
          v-show="activeName === 'b'"
          class="pl-16 pr-16"
        />
        <DressUp
          v-show="activeName === 'c'"
          class="pl-16 pr-16"
        />
        <Story
          v-model="storyVisible"
          v-show="activeName === 'd'"
          class="pl-16 pr-16"
        />
      </div>
    </div>
    <div
      class="start-btn"
      v-show="activeName === 'a'"
    >
      <van-button
        size="large"
        type="primary"
        class="w-full"
        style="border-radius: 9999px"
        @click="goChat"
      >
        {{ t('startChat') }}
      </van-button>
    </div>
    <AgentOptionPopup v-model="optionVisible" />
    <Teleport to="body">
      <ShareCard v-model="shareVisible" />
    </Teleport>
  </div>
</template>

<style scoped lang="scss">
.svg-size {
  font-size: 24px;
}

.woman-svg {
  font-size: 24px;
  color: #ff58c2;
}

.man-svg {
  font-size: 24px;
  color: #00fbfe;
}

.no-sex-svg {
  font-size: 24px;
  color: #98f;
}

.back-image {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-position: top;
  background-size: cover;
}

.top-bar {
  position: fixed;
  top: 0;
  z-index: 2;
  width: 100%;
  height: 48px;
  padding: 0 12px;
  font-size: 24px;
  background: transparent;

  .right-svg {
    height: 24px;
  }
}

.scroll-content {
  position: relative;
  height: 100%;
  overflow: auto;

  .box {
    height: 256px;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0%) 0%, rgba(0, 0, 0, 70%) 100%);
  }

  .content {
    .title-container {
      column-gap: 12px;
      padding: 0 16px 16px;
      background: linear-gradient(180deg, rgba(0, 0, 0, 70%) 0%, #000 100%);

      .message {
        display: flex;
        flex: 1;
        flex-direction: column;
        row-gap: 6px;
        overflow: hidden;

        .agent-name {
          font-size: 20px;
          font-weight: 600;
        }

        .author {
          display: flex;
          column-gap: 4px;
          align-items: center;

          img {
            flex-shrink: 0;
            width: 16px;
            height: 16px;
            border: 1px solid #98f;
            border-radius: 50%;
          }

          .name {
            overflow: hidden;
            color: rgba(153, 136, 255);
            text-overflow: ellipsis;
          }

          .agent-id {
            display: flex;
            flex-shrink: 0;
            align-items: center;
            font-size: 12px;
            color: #b2b2b2;
          }
        }

        .flag {
          display: flex;
          align-items: center;
          justify-content: flex-start;

          img {
            width: 69px;
            height: 20px;
            margin-left: 12px;
          }
        }
      }

      .like {
        .box {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          width: 48px;
          height: 48px;
          font-family: Roboto, serif;
          font-size: 10px;
          font-weight: 400;
          line-height: 12px;
          color: rgba(255, 255, 255, 70%);
          background: rgba(163, 163, 163, 20%);
          border-radius: 16px;
        }
      }
    }

    .tab-container {
      position: sticky;
      top: 48px;
      z-index: 1;
      padding: 0 16px;
      background: #000;

      :deep(.van-tab) {
        font-size: 16px;
        font-weight: 400;

        --van-tab-text-color: wihte;
      }

      :deep(.van-tab--active) {
        font-weight: 600;
        color: transparent;
        background: $livCoThemeColor;
        background-clip: text;
      }

      :deep(.van-tabs__line) {
        width: 24px !important;

        --van-tabs-bottom-bar-color: #ebdfac !important;
      }

      :deep(.van-tabs__wrap) {
        margin-left: -16px;
      }

      :deep(.van-tabs__nav) {
        margin-bottom: -1px;
        background: inherit;
      }
    }

    .tab-content {
      min-height: calc(100dvh - 92px);
      padding: 20px 0 88px;
      margin-top: -2px;
      background: #000;
    }
  }

  .start-btn {
    position: fixed;
    bottom: 20px;
    z-index: 9;
    width: 100%;
    padding: 0 16px;
  }
}
</style>
