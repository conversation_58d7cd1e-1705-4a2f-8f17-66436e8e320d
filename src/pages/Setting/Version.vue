<script setup lang="ts">
import useAppStore from '@/stores/modules/app'
import useVersionStore from '@/stores/modules/version'
definePage({
  name: 'Version',
  meta: {
    level: 2
  }
})
const versionStore = useVersionStore()
const appStore = useAppStore()
const { t } = useI18n()
</script>
<template>
  <div class="version-container">
    <van-nav-bar
      :title="t('enableNSFWOption')"
      :border="false"
      safe-area-inset-top
      :fixed="true"
      :placeholder="true"
    >
      <template #left>
        <SvgIcon
          icon-class="back"
          class="fsize-24"
          @click="$router.go(-1)"
        />
      </template>
    </van-nav-bar>
    <div class="content flex-center-center">
      <div class="logo">
        <img
          src="@/assets/images/mine/livco-logo.png"
          alt=""
        />
      </div>
      <div class="mt-24 mb-8 app-name">LIVCO</div>
      <div class="version-number">v{{ appStore.systemInfo.appVersion }}</div>
      <div class="btn-wrap">
        <div
          class="check-btn flex-center-center"
          @click="versionStore.getLatestVersionHandle('version')"
        >
          {{ t('checkForUpdates') }}
        </div>
        <div class="version-number msg-text mt-42">COPYRIGHT@2013-2024</div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.version-container {
  position: relative;
  height: 100%;
}

.content {
  flex-direction: column;
  padding: 100px 16px 8px;

  .logo {
    width: 113px;
    height: 113px;
    overflow: hidden;
    border-radius: 24px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .app-name {
    font-size: 24px;
    font-weight: 500;
    line-height: 24px;
    color: #fff;
  }

  .version-number {
    font-size: 16px;
    line-height: 16px;
    color: #525252;
  }

  .check-btn {
    min-width: 196px;
    height: 52px;
    padding: 18px 32px;
    color: #2f2101;
    background: $livCoThemeColor;
    border-radius: 16px;
  }

  .msg-text {
    font-size: 12px;
    text-align: center;
  }

  .btn-wrap {
    position: absolute;
    bottom: 16px;
  }
}
</style>
