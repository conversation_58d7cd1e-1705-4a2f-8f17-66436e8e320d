<script setup lang="ts">
import { file_upload, updateUser } from '@/api/mine/index'
import SparkMD5 from 'spark-md5'
import useUserStore from '@/stores/modules/user'
import type { UploaderFileListItem } from 'vant'
import Popup from '@/components/Popup.vue'
import Cropper from './Components/Cropper.vue'
import { useModal } from '@/hooks/useModal'

definePage({
  name: 'Information',
  meta: {
    level: 2
  }
})
const router = useRouter()
const userStore = useUserStore()
const { t } = useI18n()
const name = ref('')
const sexList = ref([
  { label: t('maleOption'), value: 1, icon: 'mans' },
  { label: t('femaleOption'), value: 2, icon: 'girls' },
  { label: t('nonBinaryOption'), value: 3, icon: 'nosex' }
])
const current = ref(-1)
const currentDate = ref(['2000', '01', '01'])
const displayDate = ref('')
const showDate = ref(false)
const message = ref('')
const imgurl = ref('')
const loading = ref(false)
const file_path = ref('')
const imgTypeLimit = ['jpg', 'jpeg', 'png', 'webp', 'gif']
const popupRef = ref<InstanceType<typeof Popup> | null>(null)
const showCrop = ref(false)
const fileUrl = ref('')

const showCropper = () => {
  showCrop.value = true
  popupRef.value.showPopup()
}
const closePopup = () => {
  showCrop.value = false
  popupRef.value.closePopup()
}
function confirm() {
  if (!displayDate.value) {
    displayDate.value = currentDate.value.join('-')
  }
  showDate.value = false
}
function init() {
  name.value = userStore.userInformation?.name
  imgurl.value = userStore.userInformation?.avatar_url
  displayDate.value = userStore.userInformation?.birthday
  message.value = userStore.userInformation?.desc
  current.value = sexList.value.findIndex((item) => item.value === userStore.userInformation?.sex)
  currentDate.value = userStore.userInformation?.birthday?.split('-') || ['2000', '01', '01']
}
function changeDate(e: { selectedValues: string[] }) {
  displayDate.value = e.selectedValues[0] + '-' + e.selectedValues[1] + '-' + e.selectedValues[2]
}

function getFileMD5(file: File) {
  return new Promise<string>((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = function (e) {
      const binary = e.target.result
      const spark = new SparkMD5.ArrayBuffer()
      spark.append(binary as ArrayBuffer)
      const md5 = spark.end()
      resolve(md5)
    }
    reader.onerror = function (e) {
      reject(e)
    }
    reader.readAsArrayBuffer(file)
  })
}
const afterRead = async (file: UploaderFileListItem | UploaderFileListItem[]) => {
  let md5 = await getFileMD5((file as UploaderFileListItem).file)
  let formdata = new FormData()
  formdata.append('file', (file as UploaderFileListItem).file)
  formdata.append('type', 'image')
  formdata.append('file_md5', md5)
  file_upload(formdata).then((res) => {
    // file_path.value = res.data.file_path
    fileUrl.value = res.data.url
    showCropper()
  })
}
const afterCrop = async ({ blob, base64 }: { blob: Blob; base64: string }) => {
  const loadingModal = useModal({
    message: t('uploadPictures'),
    autoClose: false,
    loading: true
  })
  const separateArr = imgurl.value.split('.')
  const type = separateArr[separateArr.length - 1]
  const file = {
    message: '',
    status: '',
    content: base64,
    objectUrl: URL.createObjectURL(blob)
  } as UploaderFileListItem
  file.file = new File([blob], `avatar.${type}`, { type: `image/${type}` })
  let md5 = await getFileMD5(file.file)
  let formdata = new FormData()
  formdata.append('file', file.file)
  formdata.append('type', 'image')
  formdata.append('file_md5', md5)
  file_upload(formdata)
    .then((res) => {
      imgurl.value = res.data.url
      file_path.value = res.data.file_path
    })
    .finally(() => {
      loadingModal.close()
    })
}
function submit() {
  if (loading.value) return
  let data = {
    name: name.value,
    avatar_url: file_path.value,
    birthday: displayDate.value,
    desc: message.value,
    sex: sexList.value[current.value]?.value
  }
  loading.value = true
  updateUser(data)
    .then(() => {
      userStore.getUserInfo()
      loading.value = false
      // showSuccessToast(t('successfullySaved'))
      useModal({ message: t('successfullySaved'), duration: 1500 })
      setTimeout(() => {
        router.go(-1)
      }, 1600)
    })
    .finally(() => {
      loading.value = false
    })
}
init()
</script>
<template>
  <div class="information-container">
    <van-nav-bar
      :title="t('basicInfo')"
      :border="false"
    >
      <template #left>
        <SvgIcon
          icon-class="back"
          class="fsize-24"
          @click="$router.go(-1)"
        />
      </template>
    </van-nav-bar>
    <div
      class="content padding-16"
      style="position: relative"
    >
      <van-uploader
        class="ml-10"
        :accept="imgTypeLimit.map((item) => 'image/' + item).join(', ')"
        max-count="1"
        :max-size="1024 * 1024 * 2"
        :after-read="afterRead"
      >
        <van-image
          style="position: relative"
          round
          width="80"
          height="80"
          :src="imgurl"
        />
        <img
          class="camera"
          src="@/assets/images/mine/upload-icon.png"
        />
        <!-- <img
        src="@/assets/images/agentCreate/cut.png"
        class="cut"
        @click="showCropper"
      /> -->
      </van-uploader>
      <Popup
        ref="popupRef"
        style="width: 100%; max-width: 100%; padding: 15px 0; background-color: transparent"
      >
        <Cropper
          v-if="showCrop"
          :img-url="fileUrl"
          :closePopup="closePopup"
          @after-crop="afterCrop"
        />
      </Popup>
      <div class="title">{{ t('nicknameOption') }}</div>
      <van-field
        v-model="name"
        maxlength="20"
      >
        <template #button> {{ name?.length }}/20 </template>
      </van-field>
      <div class="title">{{ t('genderOption') }}</div>
      <div class="sex flex-between-center fsize-14">
        <div
          :class="{ sexItem: true, 'flex-center-center': true, 'flex-column': true, lightSex: current === i }"
          v-for="(item, i) in sexList"
          :key="item.value"
          @click="current = i"
        >
          <SvgIcon
            :icon-class="item.icon"
            class="fsize-22"
          />
          {{ item.label }}
        </div>
      </div>
      <div class="title">{{ t('birthdaySelection') }}</div>
      <div
        class="birth flex-between-center"
        @click="showDate = true"
      >
        <span v-if="!displayDate"></span>
        <span v-else>{{ displayDate }}</span>
        <span
          class="placeholder-text"
          v-if="!displayDate"
          >{{ t('selectBirthday') }}</span
        >
        <SvgIcon
          icon-class="down-arrow"
          class="fsize-22"
        />
      </div>
      <div class="title flex-between-center">
        {{ t('personalDescription') }}
        <!-- <SvgIcon
          icon-class="person-desc"
          class="fsize-50"
        /> -->
      </div>
      <div class="textarea">
        <!-- t('youRoleChatPersonalDesc') -->
        <van-field
          v-model="message"
          rows="4"
          autosize
          type="textarea"
          maxlength="150"
          show-word-limit
          :placeholder="''"
        />
      </div>
      <div class="submit-btn">
        <div
          class="buling flex-center-center"
          @click="submit"
        >
          <span>{{ t('saveInfomation') }}</span>
          <van-loading
            v-if="loading"
            type="spinner"
            color="white"
            size="16px"
          />
        </div>
      </div>
    </div>
    <van-popup
      v-model:show="showDate"
      position="bottom"
      :style="{
        background: '#232222',
        'border-radius': '24px 24px 0px 0px'
      }"
      @click-overlay="showDate = false"
      :close-on-click-overlay="false"
    >
      <div class="mt-24 mb-24 pr-16 pl-16 flex-between-center">
        <div class="birth-title">{{ t('selectBirthday') }}</div>
        <van-icon
          name="cross"
          :size="24"
          color="white"
          @click="showDate = false"
        />
      </div>

      <van-date-picker
        v-if="showDate"
        v-model="currentDate"
        :show-toolbar="false"
        @change="changeDate"
        :min-date="new Date(1900, 0, 1)"
        :max-date="new Date()"
      />
      <div
        class="buling flex-center-center"
        @click="confirm"
      >
        {{ t('confirmSelection') }}
      </div>
    </van-popup>
  </div>
</template>
<style lang="scss" scoped>
:deep(.van-nav-bar) {
  --van-nav-bar-height: 48px;

  position: fixed;
  top: 0;
  width: 100vw;
  background-color: $livCoBackColor;
}

:deep(.van-field__control) {
  font-size: 14px;
  line-height: 18px;
  letter-spacing: 1px;
}

:deep(.van-field__control::placeholder) {
  font-size: 12px;
  line-height: 18px;
  color: #424041;
}

.information-container {
  padding-top: 48px;
  background-color: $livCoBackColor;
}

.camera {
  position: absolute;
  right: 0;
  bottom: 4px;
  width: 26px;
  height: 26px;
}

.cut {
  position: absolute;
  top: -5px;
  left: -5px;
  z-index: 1;
  width: 28px;
  height: 28px;
}

.content {
  padding-bottom: 138px;

  :deep(.van-image) {
    .van-image__img {
      border-radius: 50%;
    }
  }

  .title {
    padding: 24px 0 16px;
    font-size: 14px;
    font-weight: 500;
    color: #9fa3aa;
  }

  .van-cell {
    background: #232222;
    border-radius: 41px;
  }

  :deep(.van-field__control) {
    font-size: 16px;
    font-weight: 600;
    color: #fff;
    background: #232222;
  }

  .sexItem {
    gap: 8px;
    width: 109px;
    height: 80px;
    background: #232222;
    border-radius: 41px;
  }

  .lightSex {
    background: rgba(235, 223, 172, 10%);
    border: 1px solid #ebdfac;
  }

  .birth {
    position: relative;
    height: 48px;
    padding: 0 16px;
    font-size: 16px;
    font-weight: 600;
    color: #fff;
    background: #232222;
    border-radius: 41px;
  }

  .placeholder-text {
    position: absolute;
    top: 50%;
    left: 16px;
    font-size: 12px;
    line-height: 18px;
    color: #424041;
    transform: translateY(-50%);
  }

  .textarea {
    .van-cell {
      border-radius: 16px;
    }
  }
}

.submit-btn {
  position: fixed;
  bottom: 0;
  width: 100vw;
  height: 100px;
  margin-left: -16px;
  background-color: $livCoBackColor;

  .buling {
    position: fixed;
    bottom: 32px;
    left: 50%;
    width: 327px;
    height: 48px;
    font-size: 16px;
    font-weight: 600;
    color: $livCoTextColor;
    background: $livCoThemeColor;
    border-radius: 12px;
    transform: translateX(-50%);
  }
}

.buling {
  position: fixed;
  bottom: 32px;
  left: 50%;
  width: 327px;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  color: $livCoTextColor;
  background-color: $livCoThemeColor;
  border-radius: 40px;
  transform: translateX(-50%);
}

:deep(.van-picker) {
  margin-bottom: 80px;
  background: #232222;
}

:deep(.van-picker__frame) {
  z-index: -1;
}

:deep(.van-picker) {
  mask: linear-gradient(to bottom, transparent 0, transparent 10%, #232222, transparent 90%, transparent 100%);

  .van-picker__columns {
    position: relative;
    background: #232222;

    &::before {
      position: absolute;
      top: 41%;
      right: 0;
      left: 0;
      width: 86%;
      height: 46px;
      margin: auto;
      content: '';
      background: #2e2d2d;
      border-radius: 16px;
    }
  }
}

.birth-title {
  font-size: 18px;
  font-weight: 600;
}
</style>
<style lang="scss">
.van-theme-dark {
  --van-picker-mask-color: #232222;
}
</style>
