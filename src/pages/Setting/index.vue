<script setup lang="ts">
import useAppStore from '@/stores/modules/app'
// import { updateUser } from '@/api/mine/index'
// import useUserStore from '@/stores/modules/user'
// import { userQuery } from '@/api/mine/type'
import { getFaq } from '@/api/mine'
import { copyText } from '@/utils'

definePage({
  name: 'Set',
  meta: {
    level: 2
  }
})
const { t } = useI18n()
const router = useRouter()
const appStore = useAppStore()
// const userStore = useUserStore()
const token = ref(localStorage.getItem('access_token'))
// const tourist = ref(localStorage.getItem('tourist'))
const showLogout = ref(false)
const feedbackVisible = ref(false)
// const NSFW = ref(Boolean(userStore.userInformation.nsfw) || false)
let faqCount = sessionStorage.getItem('faqCount') || 0
const showEmail = ref(false)
function logoutFn() {
  showLogout.value = true
}
function changeLang(type: Number) {
  appStore.languagePopupTitle = type === 1 ? t('languageSwitch') : t('defaultVoicePlayback')
  appStore.languageType = type === 1 ? undefined : 'audio'
  appStore.showLangPopup = true
}

function handleClose() {
  showLogout.value = false
}
function handleSet(type: any) {
  router.push({
    name: type
  })
}
function navToHandle() {
  // 跳转
  // window.open('https://www.facebook.com/profile.php?id=61570151682130', '_blank')
  showEmail.value = true
}
// async function changeNSFW() {
//   let data: userQuery = {
//     nsfw: +NSFW.value
//   }
//   await updateUser(data)
//   await userStore.getUserInfo()
//   NSFW.value = Boolean(userStore.userInformation.nsfw)
// }
function getFaqHandle() {
  getFaq().then((res) => {
    if (res.code !== 200) return
    if (sessionStorage.getItem('faqCount') == String(res.data.count)) return
    faqCount = String(res.data.count)
    sessionStorage.setItem('faqCount', faqCount)
  })
}
getFaqHandle()
</script>
<template>
  <div class="setting-container">
    <van-nav-bar
      :title="t('setup')"
      :border="false"
    >
      <template #left>
        <SvgIcon
          icon-class="back"
          class="fsize-22"
          @click="router.push({ name: 'mine' })"
        />
      </template>
    </van-nav-bar>
    <div class="content h-full">
      <van-cell-group class="langCell">
        <van-cell
          :title="t('basicInfo')"
          is-link
          @click="handleSet('Information')"
        >
          <template #right-icon>
            <SvgIcon
              icon-class="setting-arrow"
              class="fsize-18"
            />
          </template>
        </van-cell>
        <van-cell
          :title="t('languageSwitch')"
          is-link
          :value="appStore.languageText"
          @click="changeLang(1)"
        >
          <template #right-icon>
            <SvgIcon
              icon-class="setting-arrow"
              class="fsize-18 ml-4"
            />
          </template>
        </van-cell>
        <van-cell
          :title="t('helpFAQ')"
          is-link
          @click="handleSet('FAQ')"
          v-if="Number(faqCount) > 0"
        >
          <template #right-icon>
            <SvgIcon
              icon-class="setting-arrow"
              class="fsize-18"
            />
          </template>
        </van-cell>
        <!-- <van-cell
        :title="t('defaultVoicePlayback')"
        is-link
        :value="appStore.audioLanguageText"
        @click="changeLang(2)"
      /> -->
        <van-cell
          :title="t('feedbackOption')"
          is-link
          @click="navToHandle"
        >
          <template #right-icon>
            <SvgIcon
              icon-class="setting-arrow"
              class="fsize-18"
            />
          </template>
        </van-cell>
        <!-- <van-cell
          :title="t('interestPreferences')"
          is-link
          @click="handleSet('Preference')"
        /> -->
        <!-- v-if="userStore.userInformation.invite_client_id === 0" -->
        <van-cell
          :title="t('inviteCodeEntry')"
          is-link
          @click="handleSet('Invitation')"
        >
          <template #right-icon>
            <SvgIcon
              icon-class="setting-arrow"
              class="fsize-18"
            />
          </template>
        </van-cell>
        <van-cell
          :title="t('accountDeactivation')"
          is-link
          @click="handleSet('Logoff')"
        >
          <template #right-icon>
            <SvgIcon
              icon-class="setting-arrow"
              class="fsize-18"
            />
          </template>
        </van-cell>
        <!-- <van-cell
          :title="t('captchaInvalidRetry')"
          is-link
          @click="changeNSFW"
        >
          <template #right-icon>
            <van-switch v-model="NSFW" />
          </template>
        </van-cell> -->
        <van-cell
          :title="t('report')"
          is-link
          @click="feedbackVisible = true"
        >
          <template #right-icon>
            <SvgIcon
              icon-class="setting-arrow"
              class="fsize-18"
            />
          </template>
        </van-cell>
      </van-cell-group>
      <div
        v-if="token"
        class="logout flex-center-center mt-16"
        @click="logoutFn"
      >
        {{ t('currentVersion') }}
      </div>
      <FeedbackDrawer v-model="feedbackVisible" />
    </div>
    <ConfirmLogout
      v-if="showLogout"
      @close="handleClose"
    />
    <van-dialog
      v-model:show="showEmail"
      :show-cancel-button="false"
      :show-confirm-button="false"
    >
      <div class="flex-center-center email">
        <SvgIcon
          icon-class="close"
          class="fsize-24 close-icon"
          @click="showEmail = false"
        />
        <span class="mr-4">{{ t('email') }}: </span>
        <span><EMAIL></span>
        <SvgIcon
          icon-class="copy"
          class="fsize-16 ml-4"
          @click="copyText('<EMAIL>')"
        />
      </div>
    </van-dialog>
  </div>
</template>
<style lang="scss" scoped>
:deep(.van-hairline--top-bottom::after) {
  border: none !important;
}

:deep(.van-nav-bar) {
  --van-nav-bar-height: 48px;

  position: fixed !important;
  top: 0;
  width: 100vw;
  background: transparent;
}

.setting-container {
  height: 100%;
  padding-top: 48px;
  overflow: hidden;
}

.email {
  position: relative;
  height: 150px;

  .close-icon {
    position: absolute;
    top: 16px;
    right: 16px;
  }
}

.content {
  height: calc(100vh - 48px);
  padding: 8px 16px;
  overflow-y: scroll;

  .langCell {
    overflow: hidden;
    background: #1a1a1a;
    border-radius: 16px;

    .van-cell {
      display: flex;
      align-items: center;
      padding: 14px 16px;
      background: #1a1a1a;
    }
  }

  :deep(.van-cell__title) {
    font-size: 14px;
    color: #fff;
  }

  .logout {
    width: 343px;
    height: 52px;
    font-size: 14px;
    font-weight: 500;
    color: #ff3f3f;
    background: #1a1a1a;
    border-radius: 16px;
  }
}
</style>
