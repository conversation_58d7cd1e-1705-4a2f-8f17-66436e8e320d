<script setup lang="ts">
import { copyText } from '@/utils'
import router from '@/router'
import useUserStore from '@/stores/modules/user'
import useHomeStore from '@/stores/modules/home'
import { ForwardAddressEnum } from '@/api/eventReport/index'
import Membership from '@/components/Mine/Membership.vue'
import type { IMemberInfo } from '@/api/login/types'

definePage({
  name: 'mine',
  meta: {
    level: 1
  }
})
const userStore = useUserStore()
const homeStore = useHomeStore()
const { userInformation } = toRefs(userStore)
const { t } = useI18n()
const showUnRead = computed(() => userStore.userInformation?.no_read_msg === 1)
const userToken = computed(() => userStore.userToken)
const userMemberInfo = ref<IMemberInfo>({
  vip_info: userInformation.value.vip_info,
  vip_count: userInformation.value.vip_count
})
const membershipRef = ref<InstanceType<typeof Membership> | null>()
if (userToken.value) {
  userStore.getUserInfo().then((res) => {
    if (homeStore.isUpdateData) {
      userMemberInfo.value.vip_info = res.data.vip_info
      userMemberInfo.value.vip_count = res.data.vip_count
      membershipRef.value?.findHighestVipTypeHandle()
    }
  })
}

function goMessage() {
  router.push('/Mine/Message')
}
function goSet() {
  router.push({ name: 'Set' })
}

function goProp() {
  router.push('/PropertyStore')
}
function goCrystal() {
  sessionStorage.setItem('crystal_front_address', ForwardAddressEnum.MY_PAGE)
  router.push('/Mine/Crystal')
}
// const showExchangeGoldPopup = ref<boolean>(false)
// function tabLabelClick(e: string) {
//   router.push({ name: 'search', query: { searchLabel: e } })
// }
</script>

<template>
  <div class="mine-page-container">
    <img
      src="@/assets/images/mine/min-header-bg.png"
      alt=""
      class="mine-container-header-bg"
    />
    <div class="options-banner-top flex-end-center">
      <!-- <div class="options-item mr-16">
        <a
          href="https://www.facebook.com/profile.php?id=61570151682130"
          target="_blank"
        >
          <SvgIcon
            icon-class="customer-icon"
            class="fsize-24"
          />
        </a>
      </div> -->
      <div
        class="options-item"
        @click="goMessage"
      >
        <SvgIcon
          icon-class="message-icon"
          class="fsize-24"
        />
        <div
          v-if="showUnRead"
          class="un-read-point"
        ></div>
      </div>

      <div
        class="options-item"
        @click="goSet"
      >
        <SvgIcon
          icon-class="setting-icon"
          class="fsize-24"
        />
      </div>
    </div>
    <div class="info-card">
      <div class="flex-start-center mb-24">
        <van-image :src="userInformation.avatar_url" />
        <div class="msg-box pl-16">
          <div class="name mb-8">{{ userInformation.name }}</div>
          <div class="clientId flex-start-center">
            <span>ID: {{ userInformation.client_id }}</span>
            <SvgIcon
              icon-class="copy"
              class="fsize-12 ml-4"
              @click="copyText(userInformation.client_id)"
            />
          </div>
        </div>
      </div>

      <div class="registration-days flex-between-center">
        <div>{{ t('encounteredAlready').split('X')[0] }}</div>
        <div class="flex-start-center">
          <SvgIcon
            icon-class="date-icon"
            class="fsize-20 mr-4"
          />
          <span>{{ userInformation.eros_days }}{{ t('encounteredAlready').split('X')[1] }}</span>
        </div>
      </div>

      <!-- <Member class="mt-16 mb-12" /> -->
      <Membership
        ref="membershipRef"
        :userInformation="userMemberInfo"
        class="mt-12 mb-12"
      />
      <div class="option-card flex-between-center">
        <div
          class="card-item crystal"
          @click="goCrystal"
        >
          <div class="card-icon"></div>
          <SvgIcon
            class="fsize-24"
            icon-class="crystal-mini"
          />
          <div class="crystal-count">{{ userInformation.crystal_amount }}</div>
        </div>
        <div
          class="card-item prop-item flex-center-start"
          @click="goProp"
        >
          <div class="card-icon prop"></div>
          <div class="mb-4">{{ t('store') }}</div>
          <div class="see-more flex-start-center">
            <span>{{ t('seeMore') }}</span>
            <SvgIcon
              icon-class="arrow"
              class="fsize-14 ml-2"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep(.van-image__loading) {
  background: #242424;
  border-radius: 50% !important;
}

:deep(.van-image__error) {
  background-color: #242424;
  border-radius: 50% !important;
}

.mine-page-container {
  position: relative;
  height: 100%;
  padding-bottom: 80px;
  overflow-y: scroll;
  background: $livCoBackColor;

  .mine-container-header-bg {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 0;
    width: 100%;
    height: 295px;
    pointer-events: none;
  }

  .options-banner-top {
    position: fixed;
    top: 0;
    left: 50%;
    z-index: 999;
    width: 100vw;
    padding: 12px 16px;
    transition: all 0.3s ease-in-out;
    transform: translateX(-50%);

    .options-item {
      position: relative;
      width: 24px;
      height: 24px;

      img {
        width: 100%;
        height: 100%;
      }

      &:last-child {
        margin-left: 16px;
      }

      .un-read-point {
        position: absolute;
        top: -1px;
        right: 0;
        width: 6px;
        height: 6px;
        background: #ff482b;
        border-radius: 50%;
      }
    }
  }

  .info-card {
    padding: 20px 12px;
    margin-top: 48px;
    background-color: $livCoBackColor;
    border-radius: 16px 16px 0 0;
  }

  .msg-box {
    flex: 1;
  }

  :deep(.van-image) {
    width: 88px;
    height: 88px;

    img {
      border: 2px solid #363636;
      border-radius: 50%;
    }
  }

  .name {
    font-size: 22px;
    font-weight: 500;
    line-height: 26px;
    color: #fff;
    word-break: break-all;
  }

  .clientId {
    width: fit-content;
    padding: 2px 8px;
    font-size: 12px;
    line-height: 14px;
    color: rgba(255, 255, 255, 50%);
    background: rgba(255, 255, 255, 17%);
    border-radius: 13px;
  }

  .registration-days {
    padding: 16px;
    font-size: 14px;
    line-height: 16px;
    background: #1a1a1a;
    border-radius: 12px;
  }

  .option-card {
    height: 74px;
  }

  .card-item {
    position: relative;
    width: calc((100% - 11px) / 2);
    height: 100%;
    padding: 12px 16px;
    background: #1a1a1a;
    border-radius: 12px;

    &.crystal {
      padding: 14px 13px 11px;
    }

    &.prop-item {
      flex-direction: column;
    }

    .card-icon {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 80px;
      height: 68px;
      background: url('~@/assets/images/mine/diamond-newbg.png') no-repeat;
      background-size: cover;
      border-radius: 0 0 12px;
    }
  }

  .card-icon.prop {
    width: 103px;
    height: 67px;
    padding: 19px 16px;
    background: url('~@/assets/images/mine/prop-bg.png') no-repeat;
    background-size: contain;
    border-radius: 0;
  }

  .crystal-count {
    max-width: 150px;
    font-size: 20px;
    line-height: 23px;
    word-wrap: break-word;
  }

  .see-more {
    font-size: 12px;
    line-height: 14px;
    color: #686e80;
  }
}
</style>
