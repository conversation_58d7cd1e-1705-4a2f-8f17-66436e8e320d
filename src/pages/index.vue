<script setup lang="ts">
import SwiperElegant from '@/components/Home/SwiperElegant.vue'
import AgentList from '@/components/Home/AgentList.vue'
import { eventReport, EventTypeEnum, ForwardAddressEnum } from '@/api/eventReport'
import useLoginStore from '@/stores/modules/login.ts'
import useAppStore from '@/stores/modules/app.ts'
import useUserStore from '@/stores/modules/user.ts'
import useHomeStore from '@/stores/modules/home.ts'
defineOptions({
  name: 'Home'
})
definePage({
  name: 'Home',
  meta: {
    level: 1,
    keepAlive: true
  }
})
const scrollContainerRef = ref()
const swiperRef = ref()
const topBarRef = ref()
const agentListRef = ref()

provide('scrollContainerRef', scrollContainerRef)

const loginStore = useLoginStore()
const homeStore = useHomeStore()
const route = useRoute()
const appStore = useAppStore()
const userStore = useUserStore()
const router = useRouter()
let routeType = route.query

const noLoginJumpAndPreference = () => {
  if (route.redirectedFrom?.path && route.redirectedFrom?.path !== '/') {
    nextTick(() => {
      appStore.showLogin = true
    })
  }
}

if (routeType?.invite_code) {
  localStorage.setItem('invite_code', routeType.invite_code as string)
}

const searchClick = () => {
  eventReport({
    event_type: EventTypeEnum.CLICK_SEARCH_BOX
  }).catch((err) => {
    console.warn(err)
  })
  router.push('/search')
}

watch(
  () => homeStore.isUpdateBanner,
  (value) => {
    if (value) {
      swiperRef.value.queryBannerHandle()
      // queryAlbumListHandle()
      homeStore.setUpdateBanner(false)
    }
  }
)

onActivated(() => {
  scrollContainerRef.value.scrollTop = sessionStorage.getItem('homeScroll') ?? 0
  topBarRef.value.style.background = 'transparent'
  eventReport({
    event_type: EventTypeEnum.SHOW_HOME_PAGE
  }).catch((err) => {
    console.warn(err)
  })
  noLoginJumpAndPreference()
  // 事件重新上报
  if (homeStore.isUpdateData) {
    swiperRef.value.queryBannerHandle()
    agentListRef.value.clearData()
    agentListRef.value.getAIList()
    homeStore.setUpdateData(false)
  }
  routeType = route.query
  if (routeType?.action === 'apple_login') {
    appStore.showLogin = false
    const params = {
      code: routeType.code as string,
      state: routeType.state as string,
      id_token: routeType.id_token as string
    }
    if (localStorage.getItem('loginType') === 'cancel') {
      router.push({ name: 'Logoff', query: { ...params, action: 'apple_cancel' } })
      return
    }
    loginStore.handleLogin({ type: 'apple', value: params })
  }
  if (localStorage.getItem('tourist')) {
    appStore.showLogin = Boolean(route.query.showLogin || !userStore.closeLoginDialog)
  }
  if (localStorage.getItem('access_token')) {
    appStore.showLogin = false
  } else {
    if (userStore.closeLoginDialog) {
      appStore.showLogin = false
    } else {
      appStore.showLogin = true
      sessionStorage.setItem('login_front_address', ForwardAddressEnum.HOME_PAGE)
    }
  }
})

onDeactivated(() => {
  if (routeType) {
    routeType = null
  }
})

onMounted(() => {
  scrollContainerRef.value.addEventListener('scroll', () => {
    sessionStorage.setItem('homeScroll', scrollContainerRef.value.scrollTop)
  })
})

provide('agentListRef', agentListRef)
</script>

<template>
  <div class="home-container">
    <div
      ref="topBarRef"
      class="top-bar"
      v-scroll-gradation-bg="{ scrollElement: scrollContainerRef, completedHeight: 300, color: '0,0,0' }"
    >
      <div
        class="search flex-center-center"
        @click="searchClick"
      >
        <SvgIcon
          icon-class="search-icon"
          class="fsize-20"
        />
      </div>
    </div>
    <div
      class="swiper-and-agent"
      ref="scrollContainerRef"
    >
      <div
        v-show="swiperRef?.bannerList?.length"
        class="swiper-home"
      >
        <SwiperElegant ref="swiperRef" />
      </div>
      <AgentList
        ref="agentListRef"
        :class="{ 'mt-12': !swiperRef?.bannerList?.length }"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.top-bar {
  position: absolute;
  top: 0;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  padding: 7px 16px;

  img {
    width: 100px;
  }

  .search {
    width: 34px;
    height: 34px;
    background: rgba(0, 0, 0);
    border-radius: 44px;
  }
}

.home-container {
  position: relative;
  width: 100%;
  height: 100%;

  .swiper-and-agent {
    width: 100%;
    height: 100%;
    overflow: auto;
    overscroll-behavior: none;

    .swiper-home {
      display: flex;
      flex-direction: column;
      width: 100%;
      height: 70%;
      max-height: 456px;
      background: transparent;
    }
  }
}
</style>
