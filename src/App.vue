<script setup lang="ts">
// import { storeToRefs } from 'pinia'
import useRouteCache from '@/stores/modules/routeCache'
import useAgentCreateStore from './stores/modules/agentCreate'
// import useRouteTransitionNameStore from '@/stores/modules/routeTransitionName'
import useAppStore from './stores/modules/app'
import useUserStore from './stores/modules/user'
import useHomeStore from '@/stores/modules/home'
import useLoadingStore from '@/stores/modules/loading'
// import { MQTT_LOCALSTORAGE_KEY } from '@/constant'
// import { isEmpty } from 'lodash-es'
import useNotifyStore from '@/stores/modules/notification.ts'
import NoticeAndPoster from '@/components/NoticeAndPoster/index.vue'
import { eventReport, EventTypeEnum } from '@/api/eventReport'
import { navigatorLanguage } from '@/utils/lang'
import { getDeviceFingerprint } from '@/utils/tools'
// useHead({
//   title: 'Vue3 Vant Mobile',
//   meta: [
//     {
//       name: 'description',
//       content: 'Vue + Vite H5 Starter Template',
//     },
//     {
//       name: 'theme-color',
//       content: () => isDark.value ? '#00aba9' : '#ffffff',
//     },
//   ],
//   link: [
//     {
//       rel: 'icon',
//       type: 'image/svg+xml',
//       href: () => preferredDark.value ? '/favicon-dark.svg' : '/favicon.svg',
//     },
//   ],
// })
const route = useRoute()
const router = useRouter()
const homeStore = useHomeStore()
// const routeTransitionNameStore = useRouteTransitionNameStore()
// const { routeTransitionName } = storeToRefs(routeTransitionNameStore)
const loadingStore = useLoadingStore()
const agentCreateStore = useAgentCreateStore()
const appStore = useAppStore()
const userStore = useUserStore()
const notifyStore = useNotifyStore()

const keepAliveRouteNames = computed(() => {
  return useRouteCache().routeCaches as string[]
})

const handleClose = () => {
  userStore.closeLoginDialog = true
  appStore.showLogin = false
  if (homeStore.isUpdateData) {
    router.go(0)
  }
}

function openPreference() {
  appStore.showPreference = true
}

let firstTimeOpening = sessionStorage.getItem('enterApp') || 'first'
watch(route, () => {
  // 首次进入不弹登录弹窗
  userStore.closeLoginDialog = true
  if (firstTimeOpening === 'enter') return
  firstTimeOpening = 'enter'
  sessionStorage.setItem('enterApp', 'enter')
  eventReport({ event_type: EventTypeEnum.ENTER_APP, front_address: route?.name as string }).catch(() => {})
})

navigatorLanguage()

document.addEventListener('visibilitychange', () => {
  if (document.visibilityState !== 'visible') {
    Howler.stop()
  }
})
onMounted(() => {
  console.log('and', window.JsAndroid)
  if (typeof window !== 'undefined' && (window as any).JsAndroid) {
    console.log('android')
    appStore.isAndroid = true
  }
  getDeviceFingerprint()
})
onUnmounted(() => {
  notifyStore.mqttInstance.disconnect()
})
</script>

<template>
  <VanConfigProvider
    theme="dark"
    class="w-full h-full"
  >
    <NavBar />
    <div
      class="w-full"
      :class="(route.meta.level as number) > 1 && route.meta.title ? 'had-bar-height' : 'h-full'"
    >
      <div
        v-if="loadingStore.isLoading"
        id="initial-loading"
      >
        <div class="loading-box">
          <div class="gl-container">
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
          </div>
        </div>
      </div>
      <router-view v-slot="{ Component, route }">
        <!--        <transition :name="routeTransitionName">-->
        <keep-alive :include="keepAliveRouteNames">
          <component
            :is="Component"
            :key="route.name"
          />
        </keep-alive>
        <!--        </transition>-->
      </router-view>
    </div>
    <TabBar />
    <CreateAgentSelectPopup :show="agentCreateStore.showAgentCreateModePopup" />
    <Login
      @close="handleClose"
      @go-preference="openPreference"
    />
    <PreferencePopup />
    <CancelLogoffPopup />
    <LangPopup v-if="appStore.showLangPopup" />
    <AgreementPopup v-if="appStore.showAgreement" />
    <MemberGuide v-model="appStore.showMemberGuide" />
    <!-- 消息、海报通知，只在首次登录\每次登录\每天一次展示 -->
    <NoticeAndPoster ref="newNoticeRef" />
    <ExchangeGold v-model="appStore.showCoinExchange" />
    <NewerPopup />
  </VanConfigProvider>
</template>
<style lang="scss" scoped>
.had-bar-height {
  height: calc(100% - var(--van-nav-bar-height));
  padding-top: var(--van-nav-bar-height);
}
</style>
