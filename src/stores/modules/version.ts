import { defineStore } from 'pinia'
import { compareVersions } from '@/utils/versionUpdate'
import { getLatestVersion } from '@/api/version'
import { IUpdateInfo } from '@/api/version/types'
import { useModal } from '@/hooks/useModal'
import useAppStore from '@/stores/modules/app'

const useVersionStore = defineStore('version', () => {
  const { t } = useI18n()
  const appStore = useAppStore()
  const isShowEntryApp = ref(false)
  const versionUpdateInfo = ref<IUpdateInfo>({
    auth_key: '',
    create_time: '',
    description: '',
    download_url: '',
    file_info: [],
    force_update: 0,
    id: 0,
    type: '',
    update_content: '',
    update_count: 0,
    update_path: '',
    update_time: '',
    version_code: '',
    version_name: '',
    update_type: 0
  })

  const getLatestVersionHandle = (type: 'home' | 'version') => {
    getLatestVersion(appStore.terminal).then((res) => {
      if (!res.data.version_name && type === 'version') {
        useModal({
          message: t('latestVersion'),
          duration: 2000
        })
        return
      }
      versionUpdateInfo.value = res.data
      const compareRes = compareVersions(appStore.systemInfo.appVersion, res.data.version_name)
      console.log(compareRes, 'compareRes')

      if (compareRes) {
        const laterUpdateCode = localStorage.getItem('laterUpdateCode') || ''
        if (type === 'home' && res.data.update_type === 3) {
          appStore.showVersionPopup = true
          if (laterUpdateCode) {
            localStorage.removeItem('laterUpdateCode')
          }
        }
        if (type === 'home' && res.data.update_type === 2 && laterUpdateCode != res.data.version_code) {
          isShowEntryApp.value = true
          appStore.showVersionPopup = true
          if (laterUpdateCode) {
            localStorage.removeItem('laterUpdateCode')
          }
        }
        if (type === 'version') {
          appStore.showVersionPopup = true
        }
      }

      if (!compareRes && type === 'version') {
        useModal({
          message: t('latestVersion'),
          duration: 2000
        })
      }
    })
  }
  return {
    isShowEntryApp,
    versionUpdateInfo,
    getLatestVersionHandle
  }
})

export default useVersionStore
