import { defineStore } from 'pinia'
import { user_info } from '@/api/login'
import { setLoginId, setToken, removeToken, removeLoginId, setTourist, removeTourist, getToken, getTourist, setUserInfo, removeUserInfo } from '@/utils/auth.ts'
import type { UserInfo } from '@/api/login/types'
import useAppStore from './app'
import useNotifyStore from '@/stores/modules/notification.ts'
import { MQTT_LOCALSTORAGE_KEY } from '@/constant'
const useUserStore = defineStore(
  'user',
  () => {
    const appStore = useAppStore()
    const userInformation = ref(JSON.parse(localStorage.getItem('userInformation')))
    const userToken = ref(getToken())
    const token = computed(() => userToken.value)
    const tourist = getTourist()
    const isl2d = ref(false)
    const closeLoginDialog = ref(false)
    const newerOption = ref({
      hadSwitch: false,
      hadSwitchToClearMode: false,
      hadTouch: false,
      hadChangeSelfImage: false
    })

    const saveLoginInfo = (val: any) => {
      const { login_id, access_token, first_login, ...mqtt_config } = val
      setLoginId(login_id)
      setToken(access_token)
      userToken.value = access_token
      localStorage.setItem(MQTT_LOCALSTORAGE_KEY, JSON.stringify(mqtt_config))
      getUserInfo().then(() => {
        // if (first_login) {
        // appStore.showNewer = true
        // } else {
        const notifyStore = useNotifyStore()
        notifyStore.mqttConfig = mqtt_config
        // 登录后显示开屏海报消息
        if (!appStore.isUg) {
          console.log(11212121)
          notifyStore.initNoticeAndPoster(true)
        }
        // }
      })
      removeTourist()
    }
    const touristLogin = () => {
      removeToken()
      removeLoginId()
      setTourist()
    }
    const getUserInfo = (): Promise<ResultData<UserInfo>> => {
      return new Promise((resolve, reject) => {
        user_info()
          .then((res) => {
            if (res.code === 200) {
              if (!res.data.avatar_url) {
                res.data.avatar_url = (import.meta.env.VITE_APP_PUBLIC_PATH === '/' ? '' : import.meta.env.VITE_APP_PUBLIC_PATH) + '/setting/default-avatar.png'
              }
              setUserInfo(JSON.stringify(res.data))
              userInformation.value = res.data
              if (res.data.is_delete === 1) {
                appStore.showCancelLogoff = true
              }
              resolve(res)
            }
          })
          .catch((err) => {
            reject(err)
          })
      })
    }
    const updateUserToken = () => {
      userToken.value = ''
    }
    const clearUserInfo = () => {
      removeUserInfo()
      userInformation.value = {}
    }

    return {
      saveLoginInfo,
      getUserInfo,
      userInformation,
      touristLogin,
      token,
      tourist,
      closeLoginDialog,
      newerOption,
      updateUserToken,
      userToken,
      isl2d,
      clearUserInfo
    }
  },
  {
    persist: {
      paths: ['newerOption', 'isl2d', 'closeLoginDialog']
    }
  }
)

export default useUserStore
