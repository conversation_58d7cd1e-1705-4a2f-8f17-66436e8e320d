import { defineStore } from 'pinia'
import type { ConfigProviderTheme } from 'vant'
import useUserStore from '@/stores/modules/user.ts'
export interface AppStore {
  switchMode: (val: ConfigProviderTheme) => void
}

const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
const useAppStore = defineStore(
  'app',
  () => {
    const { t } = useI18n()
    const isUg = false
    const isAndroid = ref(false)
    const theme = prefersDark ? 'dark' : 'light'
    const mode = ref<ConfigProviderTheme>(theme)
    const showLangPopup = ref(false)
    const languageType = ref('')
    const languagePopupTitle = ref(t('languageSwitch'))
    const language = localStorage.getItem('language') || 'en'
    const languageText = localStorage.getItem('languageText') || 'English'
    const audioLanguage = localStorage.getItem('audioLanguage') || 'en'
    const audioLanguageText = localStorage.getItem('audioLanguageText') || 'English'
    const showLogin = ref(false)
    const showCoinExchange = ref(false)
    const showPreference = ref(false)
    const showMemberGuide = ref(false)
    const showCancelLogoff = ref(false)
    const showAgreement = ref(false)
    const showNewer = ref(false)
    const backHomeShouldShowPreference = ref(false)
    const switchMode = (val: ConfigProviderTheme) => {
      mode.value = val
    }

    const getSystemVolume: () => Promise<boolean> = () => {
      return new Promise((resolve) => {
        const userStore = useUserStore()
        if (!isAndroid.value) {
          resolve(userStore.userInformation.bgm)
        }
        window.ug_getStreamVolume = ({ volume }) => {
          resolve(userStore.userInformation.bgm && volume !== 0)
        }
        setTimeout(() => {
          resolve(false)
        }, 1500)
        window.OG_H5_GAME_SDK.getStreamVolume()
      })
    }

    return {
      isUg,
      isAndroid,
      languageType,
      languagePopupTitle,
      mode,
      showLangPopup,
      showCoinExchange,
      language,
      languageText,
      audioLanguage,
      audioLanguageText,
      showLogin,
      showPreference,
      showMemberGuide,
      showCancelLogoff,
      showAgreement,
      showNewer,
      backHomeShouldShowPreference,
      switchMode,
      getSystemVolume
    }
  },
  {
    persist: {
      storage: sessionStorage,
      paths: ['showCancelLogoff']
    }
  }
)

export default useAppStore
