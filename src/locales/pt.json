{"welcomeToEros": "Bem-vindo ao Ugenie!", "termsConfirmation": "Por favor, confirme os seguintes termos antes de usar este site", "ageConfirmation": "Tenho 18 anos ou mais", "aiContentNotice": "Reconheço que o conteúdo relacionado à IA neste site é fictício", "registrationLogin": "Registrar-se/Fazer login", "guestLogin": "<PERSON><PERSON><PERSON><PERSON>", "language": "Língua", "emailLogin": "Login de e-mail", "email": "Email", "captchaInput": "Código de verificação", "getCaptcha": "Obter", "emailLoginOption": "<PERSON><PERSON>", "agreeToTerms": "Ao efetuar login, você concorda com a Política de Privacidade e o Acordo do Usuário.", "loginIssueContact": "Não consegue fazer login? Entre em contato com o suporte", "enterEmail": "Digite seu email", "enterCaptcha": "Insira o código de verificação", "invalidEmailFormat": "O formato do email está incorreto, por favor, digite novamente", "captchaError": "O código de verificação está incorreto, por favor, insira novamente", "captchaExpired": "O código de verificação expirou, por favor, insira novamente", "accountSuspendedNotice": "Sua conta foi suspensa, para de<PERSON>hes, você pode verificar o Centro de Ajuda para possíveis razões ou contatar o suporte ao cliente.", "selectNumber": "Escolha seu número", "skipOption": "<PERSON><PERSON>", "genderInterest": "<PERSON><PERSON><PERSON><PERSON> que você gosta", "interests": "Interesse", "allOptions": "<PERSON><PERSON>", "confirmSelection": "Confirmar", "aiFilter": "Seleção de IA", "sortOptions": "Método de ordenação", "aiType": "Tipo de IA", "mostPopular": "Mais like", "latest": "Novo", "trending": "Popular", "searchRoleOrCreator": "Procure por personagens ou criadores", "searchHistory": "Histórico de pesquisa", "popularRoles": "Personagens populares", "noResultsFeedback": "Não encontrou o que queria? Clique em feedback", "emptySearchSuggestion": "Está vazio aqui, tente outra palavra-chave?", "creatorSelection": "<PERSON><PERSON><PERSON>", "chatNow": "Cha<PERSON>", "followedNotice": "<PERSON><PERSON><PERSON>", "discoverUpdate": "Nova versão encontrada!", "updateNow": "Atualize", "updateLater": "Não atualize", "checkForUpdates": "Verificar atualização", "dynamicInteraction": "Experimente interações dinâmicas com personagens, tornando a comunicação mais vívida e interessante", "swipeToClear": "Deslize para a esquerda para limpar a tela e entrar no modo de imersão 3D", "triggerActions": "Clique em áreas especiais para ativar ações!", "chatBasedOnPersona": "Os personagens farão chat com você com base em sua configuração de personagem. Escolha seu cartão de configuração de personagem para que ela/ele possa entender você melhor", "aiContentDisclaimer": "Tudo o que a IA diz é fictício, por favor, discerna com cuidado", "changeTopicSuggestion": "Por favor, mude o tópico", "makeCall": "Ligar", "giftItems": "Presente", "startNewChat": "Reiniciar", "userCharacterOption": "Minha configuração de personagem", "startTyping": "Comece a digitar", "resetCharacterNotice": "Após a reinicialização, seu histórico de chat com o personagem será apagado.", "cancelButton": "<PERSON><PERSON><PERSON>", "cancelCreate": "Cancelar a criação", "newChatStartedNotice": "Uma nova conversa foi iniciada, as novas mensagens não são afetadas acima", "emptyContentVoicePlayback": "Você iniciou um novo tópico", "recognitionFailedRetry": "O conteúdo está vazio, não é possível reproduzir áudio", "unclearAudioRepeat": "A reconhecimento falhou, por favor, tente novamente", "voiceNotHeardNotice": "Eu não ouvi bem, você pode dizer novamente?", "continueConversation": "O personagem não poderá ouvi-lo e encerrará automaticamente a chamada após uma contagem regressiva", "exitOption": "Continuar a conversa", "clearAudioEnvironment": "Manter um ambiente acústico limpo durante a chamada", "purchaseCallDuration": "Recarregue para obter mais tempo de fala do personagem", "hangUp": "<PERSON><PERSON><PERSON>", "microphoneOn": "Microfone ligado", "showKeyboard": "Levantar o teclado", "purchaseDuration": "Tempo de chamada", "roleSpeakingDuration": "<PERSON>nte o discurso do personagem consome esse tempo", "remainingDuration": "Tempo de chamada restante", "exchangeDuration": "Obter mais tempo de chamada", "callDuration": "Duração da chamada", "repeatedClickNotice": "<PERSON>oc<PERSON> clicou várias vezes, por favor, tente novamente mais tarde", "membershipBenefits": "Tornar-se <PERSON> para obter mais cristais", "unlockSelfie": "Desbloquear selfie", "totalCrystalUsed": "<PERSON><PERSON><PERSON> acumula<PERSON> de <PERSON>ristais", "confirmButton": "Confirmar", "insufficientBalance": "<PERSON><PERSON> insuficiente, por favor recarregue", "currentLevel": "<PERSON><PERSON><PERSON> atual", "increaseIntimacy": "Aumentar o nível de intimidade para desbloquear mais maneiras de jogar", "intimacyLevelUp": "Nível de intimidade", "giftReceivedNotice": "**<PERSON><PERSON>u seu **", "giftBoostsIntimacy": "<PERSON><PERSON> presentes, pode aumentar a intimidade~", "sendGift": "<PERSON><PERSON> um presente", "itemPurchase": "<PERSON><PERSON><PERSON> itens", "unlockedFeatures": "Não desblo<PERSON>ado", "purchaseOutfit": "Comprar visual", "unlock3DOutfit": "Desbloquear visual 3D", "setAsChatBackground": "Definir como plano de fundo do chat", "exitImmersiveMode": "Sair do modo de imersão", "previousChats": "<PERSON><PERSON><PERSON>", "myCreations": "Minhas criações", "loginToView": "Login para ver", "emptyDiscover": "Está vazio aqui, vá para a página de descoberta", "goDiscover": "<PERSON><PERSON> <PERSON>", "createLover": "Crie seu próprio amado", "goCreate": "<PERSON><PERSON> criar", "followedYour": "** seguiu seu **", "followedYouNotice": "Segu<PERSON> você", "messageNotification": "Notificação", "systemMessages": "Aviso do sistema", "interactiveMessages": "Aviso interativo", "reportFeedback": "Feedback sobre den<PERSON><PERSON><PERSON>", "deleteRole": "Excluir personagem", "aboutRole": "Sobre ele/ela", "photoAlbum": "<PERSON>l<PERSON><PERSON>", "outfits": "<PERSON><PERSON><PERSON>", "characterSetting": "Design do personagem", "clickToSet": "Clique para configurar", "introduction": "Introdução", "editOption": "<PERSON><PERSON>", "openingLine": "Apresentação", "startChat": "Iniciar conversa", "retainCharacterNotice": "Você quer manter seu personagem?", "dataClearWarning": "Todos os dados dos personagens serão completamente apagados, por favor, tome cuidado", "improveAffection": "Aumentar a afinidade, desbloquear selfies exclusivos", "receiveSelfiesHere": "As selfies recebidas serão salvas aqui", "selfieUnlocked": "Desbloqueado selfies", "saveToLocal": "<PERSON><PERSON>", "staticOutfit": "Visual estático", "unlock3DOption": "Desbloquear 3D", "unlockOutfitCost": "Desbloquear um visual consome cristais", "clickToTouchSpace": "Entre na área tátil", "creationMethod": "Método de criação", "quickCreate": "Criar rapidamente", "selectGender": "Escolha o gênero do personagem", "maleOption": "<PERSON><PERSON><PERSON><PERSON>", "femaleOption": "Feminino", "nonBinaryOption": "<PERSON><PERSON>", "setPublic": "Definir como público", "publicSettingWarning": "Se definido como público, não pode ser tornado privado", "personality": "Personalidade", "createAndStartChat": "Criar e começar a conversar", "advancedCreation": "Criação avançada", "customizeImage": "Imagem personalizada", "freeImage": "<PERSON><PERSON> gratuita", "enterNickname": "Digite o nicknome", "completeBackground": "Complete a história", "createCharacter": "Criar personagem", "creatingCharacter": "<PERSON><PERSON><PERSON>", "regenerateOption": "Regerar", "cropAvatar": "Recortar avatar", "continueCurrentSetting": "Continuar com a configuração atual?", "modifySettingNotice": "Você pode modificar a configuração e criar uma nova imagem, mas isso consumirá cristais adicionais", "savedInDrafts": "As imagens geradas serão salvas na caixa de rascunhos", "exitAICharacterCreation": "Sair da criação da IA", "draftsFolder": "Caixa de rascunhos", "useImage": "Usar a imagem", "voiceOption": "Voz", "presetSoundLibrary": "Biblioteca de vozes predefinidas", "characterSettingRequired": "Configuração do personagem (obrigatório)", "openingLineRequired": "Apresentação (obrigatório)", "backgroundStoryRequired": "História de fundo (obrigatório)", "characterIntroRequired": "Introdução do personagem (obrigatório)", "dialogueExampleOptional": "Exemplo de diálogo (opcional)", "clickToAddUserContent": "Clique para adicionar o conteúdo da conversa do usuário", "clickToAddAIResponses": "Clique para adicionar o conteúdo da resposta da IA", "customizeImageName": "Configuração detalhada do nome de imagem personalizado", "fansOption": "<PERSON><PERSON><PERSON><PERSON>", "likesOption": "<PERSON>urt<PERSON>", "followOption": "<PERSON><PERSON><PERSON>", "upgradePlanBenefits": "Plano de Assinatura", "activateNow": "<PERSON><PERSON><PERSON><PERSON>", "itemStore": "<PERSON>ja de itens", "imageOption": "Aparência", "characterOption": "Personagem", "noAIsNotice": "Ainda não há uma entidade IA, clique no + abaixo para convocar uma entidade IA~", "myImageOption": "Minha aparência", "addOption": "<PERSON><PERSON><PERSON><PERSON>", "editImage": "Editar <PERSON>", "nicknameOption": "Nicknome", "genderOption": "<PERSON><PERSON><PERSON><PERSON>", "birthdaySelection": "Aniversário", "personalDescription": "Descrição pessoal", "youRoleChatPersonalDesc": "Seu personagem fará chat com você com base em sua descrição pessoal, você pode inserir seus preferências, coisas que você odeia e outras informações~", "selectBirthday": "Escolher o aniversário", "saveButton": "<PERSON><PERSON>", "unfollowOption": "<PERSON><PERSON><PERSON>", "emptySpaceNotice": "Está vazio aqui", "basicInfo": "<PERSON><PERSON>", "languageSwitch": "Idioma", "helpFAQ": "Apoio FAQ", "defaultVoicePlayback": "Reprodução de voz padrão", "feedbackOption": "Contate-Nos", "interestPreferences": "Preferências de interesse", "inviteCodeEntry": "Código <PERSON> Convi<PERSON>", "enterInviteCode": "Digite o código de convite", "invalidInviteCode": "<PERSON><PERSON><PERSON>", "rewardAlreadyClaimed": "A recompensa já foi resgatada", "accountDeactivation": "Excluir conta", "deactivationTerms": "Termos de cancelamento", "deactivationTermsContent1": "1. <PERSON><PERSON> da conta e altere-a para a conta de login atual", "deactivationTermsContent2": "2. <PERSON><PERSON><PERSON> de sair com sucesso, você não poderá fazer login novamente.<PERSON><PERSON><PERSON>, usar ou restaurar a conta", "deactivationTermsContent3": "3. Se houver membros não expirados na conta e eles não forem usadosMoedas de ouro e diamantes serão compensados ​​juntos após o cancelamento bem-sucedido.Por favor, opere com cuidado", "deactivationTermsContent4": "4. Ap<PERSON> o logout bem-sucedido, suas informações pessoais e outros dadosO conteúdo será desconectado permanentemente e não poderá ser restaurado.", "deactivationTermsContent5": "5. Ap<PERSON> solicitar o cancelamento, a conta será retida por 15 dias.Se você fizer login novamente dentro de 15 dias, o logout será automaticamente cancelado.Se o cancelamento não for cancelado, a conta será automaticamente cancelada.", "deactivationTermsContent6": "请仔细阅读以上条款，账号注销后无法恢复，请谨慎选择。", "termsAgreement": "Eu li e concordo com os termos acima", "nextStep": "Próximo passo", "accountVerification": "Verificação de conta", "otherVerificationOptions": "Outros métodos de verificação", "deactivationConfirmation": "Leia cuidadosamente e confirme novamente", "deactivationNotice1": "1. Após o cancelamento bem-sucedido, suas informações pessoais e outros dados serão cancelados permanentemente e não poderão ser recuperados.", "deactivationNotice2": "2. Após solicitar o cancelamento, sua conta será mantida por 15 dias. Se você fizer login novamente durante esse período, o cancelamento será automaticamente cancelado. Se você não cancelar o cancelamento em 15 dias, a conta será automaticamente cancelada.", "deactivationSuccessful": "Solicitação de cancelamento de conta bem-sucedida", "verificationSuccess": "Verificação bem-sucedida", "networkErrorRetry": "<PERSON><PERSON> de rede, por favor tente novamente mais tarde", "accountVerificationFailed": "A verificação da conta falhou, a conta de e-mail é inválida", "invalidEmailVerification": "Código de verificação inválido, por favor tente novamente mais tarde", "captchaInvalidRetry": "Ativar NSFW", "enableNSFWOption": "<PERSON><PERSON><PERSON> at<PERSON>", "currentVersion": "<PERSON><PERSON>", "logoutOption": "Confirme o logout", "confirmLogout": "<PERSON><PERSON><PERSON> de sair, você não poderá conversar com a IA, mas ainda pode fazer login nesta conta", "logoutInfo": "Loja <PERSON>", "orderHistory": "Ainda não é VIP", "noMembershipActivated": "Abrir para desfrutar dos benefícios VIP", "activateVIPBenefits": "Compre", "purchaseNow": "Centro de VIP", "membershipCenter": "Confirmação de cancelamento de pedido", "orderCancellationConfirm": "Observe: A cancelamento do pedido só retorna descontos usados (como descontos de primeira compra, cupons). Se você já pagou, por favor, não cancele o pedido, caso contr<PERSON>rio, pode causar o processamento do pedido falhar, causando prejuízos para você", "cancellationNotice": "Descontos reembolsáveis para este cancelamento de pedido:", "cancellationRefundDetails": "Desconto de primeira compra para VIP", "ensureNoPaymentBeforeCancel": "Confirme que você não pagou antes de cancelar!", "closeButton": "<PERSON><PERSON><PERSON>", "cancelUnpaidOrder": "Eu não paguei, cancele o pedido imediatamente", "orderRecords": "Registro de pedidos", "membershipRecharge": "Assinatura", "crystalPurchase": "<PERSON><PERSON><PERSON>", "crystalChangeDetails": "Detalhes da mudança de cristais", "goldChangeDetails": "Detalhes da mudança de ouros", "orderType": "Tipo de pedido", "creationTime": "Hor<PERSON><PERSON>", "productName": "Nome do produto", "paymentMethod": "Método de pagamento", "actualPayment": "Pagamento real", "processingOrder": "Processando o pedido", "closedOrder": "<PERSON><PERSON><PERSON>", "cancelOrder": "Cancelar o pedido", "changeTime": "<PERSON><PERSON><PERSON><PERSON>", "records": "Registro", "quantity": "Quantidade", "superMembershipOffer": "Recarregue o SVIP e ganhe prêmios instantâneos", "dailyGiftForSuperMembers": "Apresente diária para SVIP", "createCustomImage": "Criar figura personalizada", "createAI": "Criar entidade IA", "purchaseSkin": "Comprar skin", "getSelfie": "Tira uma selfie", "purchaseCallTime": "Comprar tempo de ligação", "purchaseGift": "<PERSON><PERSON><PERSON> presente", "awaitingOrderCompletion": "Aguardar a conclusão do pedido", "orderNumber": "Número do pedido", "paymentProcessing": "Pagando com esforço", "watiForPayment": "Devido a razões dos canais de pagamento, o pagamento pode levar de 5 a 10 minutos para ser processado, por favor, aguarde pacientemente.", "uncompleted": "Incompleto", "completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "return": "Voltar", "contactSupport": "Se você tiver algum problema, por favor, contate o suporte ao cliente", "myCrystals": "<PERSON><PERSON> cristais", "crystalDetails": "Detalhes de cristais", "selectPurchaseItem": "Por favor, selecione um produto", "goldExchange": "Troca de ouros", "maxAmount": "Máximo", "exchangeOption": "Troca", "cost": "Gasto", "taskForFreeGold": "Tarefas para obter mais ouros gr<PERSON>tis", "task": "<PERSON><PERSON><PERSON><PERSON>", "dailyTasks": "<PERSON><PERSON><PERSON><PERSON>", "specialTasks": "<PERSON><PERSON><PERSON><PERSON> especiais", "checkIn": "Check-in", "rewardReceivedNotice": "**(número)** (tipo de prêmio) chegaram", "rewardClaimed": "Recebido", "goComplete": "<PERSON>r completar", "claimReward": "<PERSON><PERSON><PERSON>", "notAchieved": "Não alcan<PERSON>", "inviteFriends": "Convidar amigos", "currentInvitedCount": "Convidados atualmente: 1/10", "store": "<PERSON><PERSON>", "roleGifts": "Presentes", "purchaseOption": "<PERSON><PERSON><PERSON>", "backpack": "<PERSON><PERSON><PERSON>", "searchAgentName": "Procurar personagens", "openingLineTips": "Por favor, insira um saudação", "choiceFigureTips": "Selecionar um novo avatar não manterá as configuraç<PERSON><PERSON> atuais", "selectFigureConfirm": "¿Selecionar um avatar novo?", "createAgain": "<PERSON><PERSON><PERSON><PERSON>", "inputContentTips": "Por favor, insira o conteúdo (até 300 caracteres)", "inputEmptyTips": "Entrada não pode estar vazia!", "sexChoice": "Seleção de gênero", "audioEmpty": "Não há áudio disponível", "template": "<PERSON><PERSON>", "selectRequest": "Por favor, selecione", "agentTemplateTips": "Por favor, selecione um modelo de IA", "abandonFigureConfirm": "Regenerar o avatar vai custar cristais novamente, você deseja abandonar a imagem atual do avatar?", "tag": "Etiquetas", "tagRequired": "Etiquetas (obrigatório)", "diamond": "Diamantes", "agentInfoTips": "Por favor, preen<PERSON> as informações da IA", "backgroundStoryTips": "Insira uma história de fundo", "characterIntroTips": "Insira uma breve introdução", "dialogueExample": "Exemplos de diálogo", "agentFeatureTips": "Por favor, preen<PERSON> as características da IA", "diyFigureSave": "Os avatares criados personalmente serão salvos aqui", "cancelCreateTips": "Cancelar a criação não manterá as configurações atuais", "cancelCreateConfirm": "Você deseja cancelar a criação atual?", "createAgentSuccess": "Criação de IA bem-sucedida", "characterSetTips": "<PERSON><PERSON><PERSON> as configuraç<PERSON><PERSON> do personagem", "noHaveAi": "O outro lado não tem IA", "expand": "Expandir", "collapse": "<PERSON><PERSON><PERSON><PERSON>", "none": "<PERSON><PERSON><PERSON>", "deleting": "Excluindo", "deleteSuccessful": "Exclusão bem-sucedida", "confirmDeleteCharacter": "Você tem certeza de que deseja excluir o personagem?", "download": "Baixar", "owned": "Possu<PERSON><PERSON>", "notOwned": "Não possuido", "noData": "Sem dados disponíveis", "noCharacterGifts": "<PERSON><PERSON>", "noCharacterSkins": "<PERSON><PERSON>", "readAndConfirmed": "Eu li e confirmei", "privacyAgreement": "Política de privacidade", "userAgreement": "Acordo do usuário", "unableLogin": "Não consigo me logar?", "contactCustomer": "Contatar o suporte ao cliente", "loginAndAgree": "Faça login para concordar", "noMoreOrder": "Sem Registros", "noMoreCrystalDetails": "Sem detalhes de mudança de cristal", "noMoreGoldDetails": "Sem detalhes de mudança de moeda de ouro", "goldDetails": "Detalhes da moeda de ouro", "haveAnyQuestions": "Se você tiver alguma dúvida, por favor", "taskAcquisition": "Aquisição de tarefas", "moreFreeCoins": "<PERSON><PERSON> moe<PERSON> gr<PERSON>", "recordingPermissionError": "Não é possível obter permissão de gravação, por favor, verifique as configurações", "recordingFailed": "Não é possível gravar", "speechTooShort": "O tempo de fala é muito curto", "recordingError": "A gravação falhou", "waitForSpeaker": "Por favor, espere que a outra parte termine de falar antes de enviar", "restarting": "<PERSON><PERSON><PERSON><PERSON>", "restartSuccessful": "Reinicialização bem-sucedida", "likeCancelled": "Curtida cancelada com sucesso", "likeSuccessful": "<PERSON><PERSON>ida bem-sucedida", "dislikeCancelled": "Não gosto cancelado com sucesso", "dislikeSuccessful": "<PERSON><PERSON> gosto bem-sucedida", "conversationProcessing": "Diálogo sa<PERSON>o", "voiceTooShort": "O tempo de áudio é muito curto", "exitClearMode": "Sair do modo de tela limpa", "like": "Curtir", "dislike": "<PERSON><PERSON> gosto", "copy": "Copiar", "releaseToCancelSend": "Soltar para cancelar o envio", "releaseToSendSwipeUpCancel": "Soltar para enviar, deslize para cancelar", "holdToSpeak": "Pressione e fale", "inUse": "Em uso", "rechargeSuccessful": "<PERSON><PERSON><PERSON> bem-sucedida", "minutes": "<PERSON><PERSON><PERSON>", "exit": "<PERSON><PERSON>", "levelLocked": "Níveis <PERSON>", "levelUnlocked": "Níveis blo<PERSON>", "modelLoading": "Carregando modelo", "waitToSend": "Por favor, espere que a outra parte termine de falar antes de enviar", "connecting": "<PERSON><PERSON><PERSON><PERSON>", "microphoneOff": "Microfone desligado", "weeklyPurchaseLimit": "Este produto alcançou o limite de compra semanal", "dailyPurchaseLimit": "Este produto alcançou o limite de compra diário", "purchaseSuccessful": "Compra bem-sucedida", "totalCoinsSpent": "Consu<PERSON> acumulado de moedas de ouro", "free": "<PERSON><PERSON><PERSON><PERSON>", "rechargeToUnlock": "Obter por recarga", "moreCharacterTime": "Mais tempo de fala para o personagem", "micPermissionRequired": "Ugenie precisa da sua permissão para usar o microfone corretamente", "loading": "Carregando", "sendingGift": "<PERSON><PERSON><PERSON>", "giftSent": "Doação bem-sucedida", "settingUp": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setupSuccessful": "Configuração bem-sucedida", "picture": "Imagem", "message": "Mensagem", "noMoreImage": "Não há mais configurações de personagem por enquanto", "editFigure": "Editar configurações de personagem", "profile": "Perfil pessoal", "choosePreference": "Escolha sua preferência", "recommend": "Recomendado", "welcomeToUgenie": "Bem-vindo ao U<PERSON>", "CheckTerms": "Por favor, marque os termos", "abandonAccountCancellation": "Renunciar ao cancelamento da conta", "abandonCancellation": "Renunciar ao cancelamento", "discardLogoutPrompt": "Sua conta enviou um pedido de cancelamento e será cancelada com sucesso em [时间]. Antes que o cancelamento tenha sucesso, se você precisar restaurar a conta, por favor selecione \"Cancelar Cancelamento\" para fazer login novamente, e seu pedido será cancelado. Se você não precisar cancelar o cancelamento, pode clicar em \"Fazer login com outra conta\".", "otherLoginWay": "Usar outra conta para fazer login", "aiOnTheWay": "O IA ainda está no caminho～", "focusonSuccess": "Seguido com sucesso~", "unfollowed": "Deixado de seguir", "fansAmount": "xxx seguidores", "rolesAmount": "xxx personagens", "searchWant": "Procure o que você deseja...", "search": "<PERSON><PERSON><PERSON><PERSON>", "searchWantTips": "<PERSON><PERSON> consegue encontrar o que deseja?", "clickFeedback": "Obter Suporte", "confirmDelFigure": "Você tem certeza de que deseja excluir este avatar?", "verify": "Verificar", "emailFormatError": "Formato de e-mail incorreto", "inconsistentAccounts": "A conta verificada não é a conta atualmente logada", "personalDescriptionTips": "Seu personagem será baseado em sua descrição pessoal e suas conversas, você pode inserir seus gostos, coisas que não gosta e outras informações～", "successfullySaved": "Salvo", "uploadPictures": "Enviando imagem", "setup": "Configurações", "remainingDiamonds": "Total de Cristais", "exchangeCoins": "<PERSON><PERSON><PERSON> moedas de ouro", "redeemNow": "Trocar agora", "consumption": "Consu<PERSON>", "weeklyTasks": "<PERSON><PERSON><PERSON><PERSON>", "gold": "<PERSON><PERSON> de ouro", "crystal": "<PERSON><PERSON><PERSON>", "inviteCode": "Código de convite", "inviteAmount": "Convidou um total de xx pessoas", "chooseCombo": "Por favor, selecione um plano de membros", "NonMember": "Atualmente, nenhum plano de membros está ativo", "cashRegister": "caixa de pagamento", "selectPaymentMethod": "Selecione a forma de pagamento", "selectPaymentWay": "Selecione a forma de pagamento", "paySuccess": "Pagamento bem sucedido", "payAmount": "Valor real pago", "selectPayCountry": "Selecione o país de pagamento", "immediatePayment": "Pague agora", "orderCompleted": "Pedido concluído", "tryUsingDifferentKeyword": "Altere a palavra-chave e tente novamente", "insufficientCrystals": "Cristais insuficientes", "exchangeSuccessful": "Resgate bem-sucedido", "termTips": "Leia os termos acima com atenção. Sua conta não pode ser restaurada após ser cancelada, portanto, escolha com cuidado.", "noMore": "<PERSON><PERSON> há mais", "msgDetail": "Detalhes da mensagem", "noMoreMsg": "Não há mais mensagens", "noMoreContent": "<PERSON><PERSON> há mais conteúdo", "invitationLink": "Convite link", "loadingFailed": "O carregamento do SDK falhou", "tagEmptyTips": "Selecione o rótulo", "permissionRequire": "Aplicação de permissão", "intimacyUnlockFunc": "Aprimore sua favorabilidade desbloquear funções exclusivas", "syncPhoneCallRecord": "Os registros de chamadas de voz foram sincronizados.", "payedOrder": "Pedido pago", "refundOrder": "Pedido reembolsado", "canceledOrder": "Pedido cancelado", "pullToRefresh": "Puxe para baixo para atualizar", "looseToRefresh": "Solte para atualizar", "confirmExitStory": "Tem certeza de que deseja sair da história atual?", "savePicture": "<PERSON><PERSON> imagem", "interactWithHer": "Interagir com ela", "scanQRCode": "Escaneie o código QR para jogar novamente", "newerStoryName": "Renascimento: <PERSON><PERSON> de Lily", "flowerList": "Presentes", "popularityChart": "Popularidade", "invalidSession": "Sessão <PERSON>, faça login novamente", "pressAgain": "Pressione novamente para sair do aplicativo", "searchAgentRoles": "Procurar personagens", "day": "dias", "encounteredAlready": "Conheci Ugenie h<PERSON>as", "latestVersion": "Atualmente a versão mais recente", "seeMore": "Ver mais", "purchaseCancelled": "Compra cancelada", "buyFail": "Falha na compra", "unableConnect": "Não foi possível conectar à loja", "unableFindProduct": "O produto não existe", "refuseGenerate": "Não, obrigado", "inputFeedback": "Digite seu feedback", "thankFeedback": "G<PERSON><PERSON> por su retroalimentación", "fillInFeedback": "<PERSON><PERSON><PERSON> os comentários", "report": "<PERSON><PERSON><PERSON><PERSON>", "storyIntroduction": "Introdução", "missionObjectives": "Objetivo", "missionRewards": "Recompensa", "rewardCongratulation": "História concluída, parabéns por receber ", "generateCard": "Coletar cartão de história", "backToChat": "Voltar ao chat", "playAgain": "Jogar novamente", "myStoryBook": "Meu livro de histórias", "moreStories": "<PERSON><PERSON>", "recheckStoryRecord": "Você pode visualizá-lo novamente na seção \"História\" da \"Barra de Ferramentas da Página de Diálogo\"", "storyBookGenerateConfirm": "Deseja criar um livro de histórias?", "storyBookGenerateTips": "Jogar novamente apagará o registro atual da história. Deseja criar um livro de histórias para salvar este registro?", "agentProfile": "Perfil", "moreIntimacyToUnlock": "Aumente a intimidade para desbloquear mais conteúdo da história", "story": "História", "saveInfomation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "generateSuccessfully": "Gerado com sucesso", "generateStoryBook": "<PERSON><PERSON><PERSON>", "freeFirst": "Primeira vez gr<PERSON><PERSON>", "goToChat": "Ir para o chat", "Completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Locked": "Bloqueado", "syncGameMemory": "Memória de jogo exclusiva Lv{level} sincronizada", "clickToStory": "Clique no cartão para desbloquear a história exclusiva", "upgradeToUnlockStory": "Aumente a intimidade com o Agente para desbloquear", "copied": "Copiado", "startNow": "<PERSON><PERSON><PERSON>ra", "leaderboard": "<PERSON><PERSON><PERSON>", "featureCoding": "Novo recurso em breve", "unableToSend": "<PERSON><PERSON>", "getSuccessfully": "Obter sucesso", "freeInput": "Entrada livre...", "adsToCrystal": "Assista ao anúncio para obter 50 cristais", "selectOne": "Por favor, selecione um", "shareContent": "Compartilhe o Agente com seu amigo!", "gameWin": "Vitória do jogo", "gameLose": "<PERSON><PERSON><PERSON> no jogo", "doubleRewards": "Recompensas <PERSON>", "unlockByAds": "Desbloquear assistindo a anúncios", "rewardTip": "Parabéns por receber o prêmio", "feedback1": "Erro de conteúdo ou resposta incompleta", "feedback2": "<PERSON><PERSON><PERSON><PERSON> sens<PERSON> ou pornográfico", "feedback3": "Informações envolvendo violência ou ódio", "feedback4": "Informações inadequadas envolvendo menores", "feedback5": "<PERSON><PERSON><PERSON><PERSON>", "inputFeedbackMessage": "Por favor, insira o problema que você encontrou", "submit": "Enviar", "feedbackSuccess": "<PERSON><PERSON>, obri<PERSON> pelo <PERSON>"}