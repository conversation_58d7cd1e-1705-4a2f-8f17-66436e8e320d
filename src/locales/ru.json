{"welcomeToEros": "Добро пожаловать в Ugenie!", "termsConfirmation": "Перед использованием сайта, пожалуйста, ознакомьтесь с приведенными ниже условиями.", "ageConfirmation": "Я 18 лет или старше.", "aiContentNotice": "Я разрешаю, что AI-содержимое на сайте является выымышленным.", "registrationLogin": "Регистрация/Вход", "guestLogin": "Гостевой вход", "language": "Язык", "emailLogin": "Вход по электронной почте", "email": "Электронная почта", "captchaInput": "Код подтверждения", "getCaptcha": "Получение", "emailLoginOption": "Логин по электронной почте", "agreeToTerms": "Вход в систему означает согласие с Политикой конфиденциальности и Пользовательским соглашением.", "loginIssueContact": "Пожалуйста, свяжитесь с поддержкой, если не можете войти.", "enterEmail": "Пожалуйста, введите ваш адрес электронной почты.", "enterCaptcha": "Пожалуйста, введите код подтверждения.", "invalidEmailFormat": "Формат адреса электронной почты неправильный, пожалуйста, введите заново.", "captchaError": "Код подтверждения неправильный, пожалуйста, введите заново.", "captchaExpired": "Код подтверждения истек, пожалуйста, введите заново.", "accountSuspendedNotice": "Ваш аккаунт был приостановлен. Подробности можно узнать в центре поддержки или обратитесь в службу поддержки.", "selectNumber": "Выберите свой номер", "skipOption": "Пропустить", "genderInterest": "Ваш интерес к полу", "interests": "Интерес", "allOptions": "Все", "confirmSelection": "Подтвердить", "aiFilter": "Интеллектуальное отбор(entities)", "sortOptions": "Сортировка", "aiType": "Тип интеллектуальной сущности", "mostPopular": "Г<PERSON><PERSON><PERSON><PERSON><PERSON>", "latest": "Новый", "trending": "Популярный", "searchRoleOrCreator": "Поиск персонажей или создателей", "searchHistory": "История поиска", "popularRoles": "Популярные персонажи", "noResultsFeedback": "Не находишь того, что хочешь? Нажми обратную связь", "emptySearchSuggestion": "Здесь пусто, попробуйте другой ключевой слова?", "creatorSelection": "Создатель", "chatNow": "Чат", "followedNotice": "Подписаны", "discoverUpdate": "Новая версия найдена!", "updateNow": "Обновить сейчас", "updateLater": "Пока не обновлять", "checkForUpdates": "Проверить обновления", "dynamicInteraction": "Опыт динамного взаимодействия с персонажами, сделает общение более ярким и интересным", "swipeToClear": "Смахните влево, чтобы очистить экран и войти в режим непривычного погружения в 3D", "triggerActions": "Нажмите на особые зоны, чтобы активировать действия!", "chatBasedOnPersona": "Персонажи будут общаться с вами на основе вашей настройки персонажа. Выберите карту вашей настройки персонажа, чтобы она/он лучше вас понял", "aiContentDisclaimer": "Все, что говорит ИИ, является вымыслом, пожалуйста, внимательно отличайте", "changeTopicSuggestion": "Пожалуйста, смените тему", "makeCall": "Позвонить", "giftItems": "Подарок", "startNewChat": "Перезагрузить", "userCharacterOption": "Моя настройка персонажа", "startTyping": "Начать ввод", "resetCharacterNotice": "После перезагрузки ваша история чатов с персонажем будет очищена.", "cancelButton": "Отменить", "cancelCreate": "Отменить создание", "newChatStartedNotice": "Новый разговор начался, новые сообщения выше не затрагиваются", "emptyContentVoicePlayback": "Вы начали новый разговор", "recognitionFailedRetry": "Содержание пустое, голос не может быть воспроизведен", "unclearAudioRepeat": "Распознавание не удалось, попробуйте снова", "voiceNotHeardNotice": "Я не понял, можете повторить?", "continueConversation": "Персонаж не может слышать ваш голос, и после отсчета разговор будет автоматически завершен", "exitOption": "Продолжить разговор", "clearAudioEnvironment": "Пожалуйста, поддерживайте чистую акустическую обстановку во время разговора", "purchaseCallDuration": "Пополните счет, чтобы получить больше времени для разговора персонажей", "hangUp": "Повесить трубку", "microphoneOn": "Включить микрофон", "showKeyboard": "Поднять клавиатуру", "purchaseDuration": "Время разговора", "roleSpeakingDuration": "Только речь персонажей потребляет это время", "remainingDuration": "Оставшееся время разговора", "exchangeDuration": "Получить больше времени разговора", "callDuration": "Длительность разговора", "repeatedClickNotice": "Вы многократно нажали, попробуйте позже", "membershipBenefits": "Активируйте членство, чтобы получить больше кристаллов", "unlockSelfie": "Разблокировать selfie", "totalCrystalUsed": "Количество потраченных кристаллов", "confirmButton": "Подтвердить", "insufficientBalance": "Недостаточно средств, пополните счет", "currentLevel": "Текущий уровень", "increaseIntimacy": "Повысьте близость, чтобы разблокировать больше способов игры", "intimacyLevelUp": "Уровень близости", "giftReceivedNotice": "Получил ваш", "giftBoostsIntimacy": "Дарить подарки, можно повысить близость~", "sendGift": "Дарить подарки", "itemPurchase": "Купить предметы", "unlockedFeatures": "Не разблокирован", "purchaseOutfit": "Купить скинки", "unlock3DOutfit": "Разблокировать 3D скин", "setAsChatBackground": "Установить как фон чата", "exitImmersiveMode": "Выход из непривычного погружения", "previousChats": "Поговорили", "myCreations": "Мои создания", "loginToView": "Войдите для просмотра", "emptyDiscover": "Здесь пусто, перейдите на страницу обнаружения", "goDiscover": "Перейти к обнаружению", "createLover": "Создайте своего ловеера", "goCreate": "Перейти к созданию", "followedYour": "Отслеживание вас", "followedYouNotice": "Подписался на вас", "messageNotification": "Уведомление", "systemMessages": "Системные сообщения", "interactiveMessages": "Интерактивные сообщения", "reportFeedback": "Сообщить об обратной связи", "deleteRole": "Удалить персонажа", "aboutRole": "О Та", "photoAlbum": "Альбом", "outfits": "Наряды", "characterSetting": "Перс<PERSON><PERSON><PERSON>", "clickToSet": "Нажмите для установки", "introduction": "Краткое описание", "editOption": "Редактировать", "openingLine": "Вступительные слова", "startChat": "Диал<PERSON>ги", "retainCharacterNotice": "Хотите ли вы сохранить персонажа?", "dataClearWarning": "Все данные персонажа будут полностью удалены, пожалуйста, аккуратно", "improveAffection": "Повысьте благосклонность, чтобы разблокировать эксклюзивные selfie", "receiveSelfiesHere": "Полученные selfies будут сохранены здесь", "selfieUnlocked": "Разблокированные selfies", "saveToLocal": "Сохранить локально", "staticOutfit": "Статический наряд", "unlock3DOption": "Разблокировать 3D", "unlockOutfitCost": "Разблокировка наряда потребует кристаллов", "clickToTouchSpace": "Войти в пространство контактов", "creationMethod": "Создание", "quickCreate": "Быстрое создание", "selectGender": "Выбор пола персонажа", "maleOption": "Мужчина", "femaleOption": "Женщина", "nonBinaryOption": "бесполый", "setPublic": "Опубликовать", "publicSettingWarning": "Если опубликовать, то невозможно сделать частным", "personality": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createAndStartChat": "Создайте и начните общение", "advancedCreation": "Расширенное создание", "customizeImage": "Пользовательская графика", "freeImage": "Бесплатная графика", "enterNickname": "Введите псевдоним", "completeBackground": "Добавьте историю", "createCharacter": "Создание персонажа", "creatingCharacter": "Создание", "regenerateOption": "Перегенерировать", "cropAvatar": "Обрезать аватар", "continueCurrentSetting": "Продолжить с текущими настройками?", "modifySettingNotice": "Вы можете изменить настройки и создать новую картинку, но это потребует дополнительных кристаллов", "savedInDrafts": "Созданные изображения будут сохранены в корзине", "exitAICharacterCreation": "Выход из создания интеллектуальной сущности", "draftsFolder": "Черновики", "useImage": "Использовать изображение", "voiceOption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "presetSoundLibrary": "Предустановленная голосовая библиотека", "characterSettingRequired": "Персонаж (обязательно)", "openingLineRequired": "Участок (обязательно)", "backgroundStoryRequired": "Бэк-история (обязательно)", "characterIntroRequired": "Персонаж (обязательно)", "dialogueExampleOptional": "Диалог (опционально)", "clickToAddUserContent": "Нажмите, чтобы добавить содержание диалога пользователя", "clickToAddAIResponses": "Нажмите, чтобы добавить содержание AI-ответа", "customizeImageName": "-", "fansOption": "Фанаты", "likesOption": "Лайки", "followOption": "Подписаться", "upgradePlanBenefits": "Подписка", "activateNow": "Обновление", "itemStore": "Мага<PERSON>ин предметов", "imageOption": "Внешность", "characterOption": "Перс<PERSON><PERSON><PERSON>", "noAIsNotice": "Еще нет интеллектуальной сущности, нажмите + ниже, чтобы призвать интеллектуальную сущность~", "myImageOption": "Моя внешность", "addOption": "Добавить", "editImage": "Редактировать внешность", "nicknameOption": "Ник", "genderOption": "Пол", "birthdaySelection": "День рождения", "personalDescription": "Личное описание", "youRoleChatPersonalDesc": "Ваш персонаж будет общаться с вами на основе вашего личного описания, вы можете вводить ваши предпочтения, вещи, которые вы ненавидите, и другую информацию~", "selectBirthday": "Выбор дня рождения", "saveButton": "Сохранить", "unfollowOption": "Отписаться", "emptySpaceNotice": "Здесь пусто", "basicInfo": "Мой профиль", "languageSwitch": "Язык", "helpFAQ": "Помощь FAQ", "defaultVoicePlayback": "По умолчанию воспроизведение голоса", "feedbackOption": "Свяжитесь с нами", "interestPreferences": "Интересные предпочтения", "inviteCodeEntry": "Код приглашения", "enterInviteCode": "Пожалуйста, введите приглашение код", "invalidInviteCode": "Приглашение код не существует", "rewardAlreadyClaimed": "Премия уже получена", "accountDeactivation": "Удалить аккаунт", "deactivationTerms": "Условия отмены", "deactivationTermsContent1": "1. Учетная запись отменяется для текущей учетной записи.", "deactivationTermsContent2": "2. После успешной отмены вы больше не сможете войти в систему, активировать, использовать или восстановить эту учетную запись. ", "deactivationTermsContent3": "3. Если в учетной записи есть неиспользованные членские взносы, золотые монеты и бриллианты, они будут удалены при успешной отмене. Пожалуйста, действуйте осторожно.", "deactivationTermsContent4": "4. После успешной отмены ваши личные данные и другие данные будут удалены навсегда и не могут быть восстановлены.", "deactivationTermsContent5": "5. После запроса отмены ваша учетная запись будет храниться в течение 15 дней. Если вы входите в систему снова в течение этого периода, отмена будет автоматически отменена. Если вы не отмените отмену в течение 15 дней, учетная запись будет автоматически отменена.", "deactivationTermsContent6": "请仔细阅读以上条款，账号注销后无法恢复，请谨慎选择。", "termsAgreement": "Я ознакомлен и согласен с вышеуказанными условиями", "nextStep": "Следующий шаг", "accountVerification": "Учетная запись верификации", "otherVerificationOptions": "Другие способы проверки", "deactivationConfirmation": "Пожалуйста, внимательно прочитайте и подтвердите снова", "deactivationNotice1": "1. После успешной отмены ваши личные данные и другие данные будут навсегда отменены и не могут быть восстановлены.", "deactivationNotice2": "2. После запроса на отмену ваша учетная запись будет сохранена в течение 15 дней. Если вы снова войдете в систему в течение этого времени, отмена будет автоматически отменена. Если отмену не отмените в течение 15 дней, учетная запись будет автоматически отменена.", "deactivationSuccessful": "Успешная отмена учетной записи", "verificationSuccess": "Успешная верификация", "networkErrorRetry": "Сеть неисправна, пожалуйста, попробуйте позже", "accountVerificationFailed": "Неудачная верификация аккаунта, недействительный адрес электронной почты", "invalidEmailVerification": "Неверный код подтверждения, пожалуйста, попробуйте позже", "captchaInvalidRetry": "Включить NSFW", "enableNSFWOption": "Текущая версия", "currentVersion": "Выход", "logoutOption": "Подтвердите выход", "confirmLogout": "После выхода вы не сможете общаться с ИИ, но вы можете войти на этот аккаунт", "logoutInfo": "Магазин для членов", "orderHistory": "Еще не член", "noMembershipActivated": "Получите преимущества VIP после регистрации", "activateVIPBenefits": "Купить сейчас", "purchaseNow": "Центр <PERSON><PERSON><PERSON><PERSON>ов", "membershipCenter": "Подтверждение отмены заказа", "orderCancellationConfirm": "Обратите внимание: отмена заказа возможна только в случае возврата использованных скидок (например, скидки на первый заказ, ваучеры). Если вы уже оплатили, пожалуйста, не отменяйте заказ, иначе это может привести к неудачному обработке заказа и привести к вашим убыткам", "cancellationNotice": "Скидки на возврат при отмене заказа:", "cancellationRefundDetails": "Условия для первого заказа участника", "ensureNoPaymentBeforeCancel": "Пожалуйста, подтвердите, что вы еще не оплатили, прежде чем отменять!", "closeButton": "Закрыть", "cancelUnpaidOrder": "Я не оплатил, пожалуйста, немедленно отмените заказ", "orderRecords": "История заказов", "membershipRecharge": "Подписка", "crystalPurchase": "Кристаллы", "crystalChangeDetails": "Детализация изменения криста<PERSON>лов", "goldChangeDetails": "Детализация изменения золотых монет", "orderType": "Тип заказа", "creationTime": "Время создания", "productName": "Название продукта", "paymentMethod": "Способ оплаты", "actualPayment": "Фактическая оплата", "processingOrder": "Обработка заказа", "closedOrder": "Закрыто", "cancelOrder": "Отменить заказ", "changeTime": "Время изменения", "records": "Запись", "quantity": "Количество", "superMembershipOffer": "Мгновенная скидка на пополнение суперчленства", "dailyGiftForSuperMembers": "Ежедневный подарок супер-членам", "createCustomImage": "Создать персональный аватар", "createAI": "Создать интеллектуальную сущность", "purchaseSkin": "Купить скин", "getSelfie": "Получить selfie", "purchaseCallTime": "Купить время разговора", "purchaseGift": "Купить подарок", "awaitingOrderCompletion": "Подождите завершения заказа", "orderNumber": "Номер заказа", "paymentProcessing": "Усердно платить", "watiForPayment": "Из-за причин, связанных с каналами оплаты, обработка платежа может занять 5-10 минут, пожалуйста, подождите стерпеливо.", "uncompleted": "Незавершено", "completed": "Завершено", "return": "Возврат", "contactSupport": "Если у вас возникнут проблемы, пожалуйста, обратитесь в службу поддержки", "myCrystals": "Мои кристаллы", "crystalDetails": "Детализация криста<PERSON><PERSON>ов", "selectPurchaseItem": "Пожалуйста, выберите товар", "goldExchange": "Золотой монеты обмен", "maxAmount": "Максимум", "exchangeOption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cost": "Тратить", "taskForFreeGold": "Заработать больше бесплатных золотых монет за задания", "task": "Задания", "dailyTasks": "Ежедневные задания", "specialTasks": "Специальные задания", "checkIn": "Проверка", "rewardReceivedNotice": "(Количество) (Тип вознаграждения) было зачислено", "rewardClaimed": "Получено", "goComplete": "Выполнить", "claimReward": "Забрать", "notAchieved": "Не достигнуто", "inviteFriends": "Пригласить друзей", "currentInvitedCount": "В настоящее время приглашено: 1/10", "store": "Мага<PERSON>ин", "roleGifts": "Подарки", "purchaseOption": "Купить", "backpack": "Рюкзак", "searchAgentName": "Поиск персонажей", "openingLineTips": "Пожалуйста, введите приветствие", "choiceFigureTips": "Перевыбор аватара не сохранит текущие настройки", "selectFigureConfirm": "Перевыбрать аватар?", "createAgain": "Пересоздать", "inputContentTips": "Пожалуйста, введите содержание (до 300 символов)", "inputEmptyTips": "Ввод не может быть пустым!", "sexChoice": "Выбор пола", "audioEmpty": "Нет аудио", "template": "Шабл<PERSON>н", "selectRequest": "Пожалуйста, выберите", "agentTemplateTips": "Пожалуйста, выберите шаблон ИИ", "abandonFigureConfirm": "Перегенерация аватара снова потребует кристаллов, хотите ли вы отказаться от текущего изображения аватара?", "tag": "<PERSON><PERSON><PERSON><PERSON>", "tagRequired": "Тегi (обязательно)", "diamond": "Алмазы", "agentInfoTips": "Пожалуйста, заполните информацию об ИИ", "backgroundStoryTips": "Пожалуйста, введите историю", "characterIntroTips": "Пожалуйста, введите короткое представление", "dialogueExample": "Диалоговые примеры", "agentFeatureTips": "Пожалуйста, заполните характеристики ИИ", "diyFigureSave": "Персонально созданные аватары будут сохранены здесь", "cancelCreateTips": "Отмена создания не сохранит текущие настройки", "cancelCreateConfirm": "Вы хотите отменить текущее создание?", "createAgentSuccess": "Создание ИИ прошло успешно", "characterSetTips": "Пожалуйста, введите настройки персонажа", "noHaveAi": "У другого нет ИИ", "expand": "Развернуть", "collapse": "Свернуть", "none": "Нет", "deleting": "Удаление", "deleteSuccessful": "Успешное удаление", "confirmDeleteCharacter": "Вы уверены, что хотите удалить персонажа?", "download": "Загрузить", "owned": "Владеет", "notOwned": "Не владеет", "noData": "Нет данных", "noCharacterGifts": "Ничего нет", "noCharacterSkins": "Ничего нет", "readAndConfirmed": "Я прочитал и подтвердил", "privacyAgreement": "Политика конфиденциальности", "userAgreement": "Соглашение пользователя", "unableLogin": "Не могу войти?", "contactCustomer": "Связаться со службой поддержки", "loginAndAgree": "Войдите, чтобы согласиться", "noMoreOrder": "Нет записей", "noMoreCrystalDetails": "Нет деталей изменения криста<PERSON>лов", "noMoreGoldDetails": "Нет деталей изменения золотых монет", "goldDetails": "Детали золотых монет", "haveAnyQuestions": "Если у вас есть вопросы, пожалуйста", "taskAcquisition": "Получение задачи", "moreFreeCoins": "Больше бесплатных монет", "recordingPermissionError": "Невозможно получить разрешение на запись, пожалуйста, проверьте настройки", "recordingFailed": "Невозможно записать", "speechTooShort": "Время разговора слишком короткое", "recordingError": "Запись не удалась", "waitForSpeaker": "Пожалуйста, подождите, когда другой человек закончит говорить, прежде чем отправлять", "restarting": "Перезагрузка", "restartSuccessful": "Перезагрузка прошла успешно", "likeCancelled": "Лайк успешно отменен", "likeSuccessful": "Лайк успешно", "dislikeCancelled": "Нелайк успешно отменен", "dislikeSuccessful": "Нелайк успешно", "conversationProcessing": "Диалог выводится", "voiceTooShort": "Время голоса слишком короткое", "exitClearMode": "Выход из режима чистого экрана", "like": "Лайк", "dislike": "Нелайк", "copy": "Копировать", "releaseToCancelSend": "Отпустите, чтобы отменить отправку", "releaseToSendSwipeUpCancel": "Отпустите, чтобы отправить, проведите вверх, чтобы отменить", "holdToSpeak": "Удерживайте, чтобы говорить", "inUse": "Используется", "rechargeSuccessful": "Пополнение прошло успешно", "minutes": "Минуты", "exit": "Выход", "levelLocked": "Уровни не разблокированы", "levelUnlocked": "Уровни заблокированы", "modelLoading": "Загрузка модели", "waitToSend": "Пожалуйста, подождите, когда другой человек закончит говорить, прежде чем отправлять", "connecting": "Подключение", "microphoneOff": "Микрофон выключен", "weeklyPurchaseLimit": "Этот продукт достиг еженедельного лимита покупки", "dailyPurchaseLimit": "Этот продукт достиг ежедневного лимита покупки", "purchaseSuccessful": "Покупка прошла успешно", "totalCoinsSpent": "Акумулированное потребление золотых монет", "free": "Бесплатно", "rechargeToUnlock": "Получить пополниванием", "moreCharacterTime": "Больше времени для речи персонажа", "micPermissionRequired": "Ugenie нуждается в вашем разрешении для использования микрофона", "loading": "Загрузка", "sendingGift": "Дарение", "giftSent": "Дарение прошло успешно", "settingUp": "Настройка", "setupSuccessful": "Настройка прошла успешно", "picture": "Изображение", "message": "Сообщение", "noMoreImage": "В настоящее время больше нет настроек персонажа", "editFigure": "Редактировать настройки персонажа", "profile": "Личный профиль", "choosePreference": "Выберите ваше предпочтение", "recommend": "Рекомендуется", "welcomeToUgenie": "Добро пожаловать в Ugenie", "CheckTerms": "Пожалуйста, отметьте условия", "abandonAccountCancellation": "Отказаться от отмены учетной записи", "abandonCancellation": "Отказаться от отмены", "discardLogoutPrompt": "Ваша учетная запись подала заявку на отмену и будет успешно отменена в [时间]. Перед тем как отмена будет успешной, если вам нужно восстановить аккаунт, пожалуйста, выберите \"Отменить отмену\", чтобы снова войти в аккаунт, и ваш запрос будет отменен. Если вам не нужно отменять отмену, вы можете нажать \"Войти с другой учетной записью\".", "otherLoginWay": "Войти с другой учетной записью", "aiOnTheWay": "Интеллектуальная система еще на пути～", "focusonSuccess": "Подписка выполнена успешно~", "unfollowed": "Отписаться от подписки", "fansAmount": "xxx под<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ов", "rolesAmount": "xxx персона<PERSON>ей", "searchWant": "Ищите то, что вы хотите...", "search": "Поиск", "searchWantTips": "Не можете найти то, что хотите?", "clickFeedback": "Получить поддержку", "confirmDelFigure": "Вы уверены, что хотите удалить этого персонажа?", "verify": "Проверка", "emailFormatError": "Неправильный формат электронной почты", "inconsistentAccounts": "Проверенная учетная запись не является текущей учетной записью, с которой вы вошли", "personalDescriptionTips": "Ваш персонаж будет основан на вашем личном описании и ваших чатах, вы можете вводить свои предпочтения, нелюбимые вещи и другую информацию～", "successfullySaved": "Сохранено", "uploadPictures": "Загрузка изображения", "setup": "Настройки", "remainingDiamonds": "Всего криста<PERSON><PERSON>ов", "exchangeCoins": "Обмен золотых монет", "redeemNow": "立刻兑换", "consumption": "Расход", "weeklyTasks": "Еженедельные задачи", "gold": "Золотые монеты", "crystal": "Кристаллы", "inviteCode": "Приглашение код", "inviteAmount": "Пригласили в общем xx человек", "chooseCombo": "Пожалуйста, выберите план членства", "NonMember": "В настоящее время нет активной подписки", "cashRegister": "Касса", "selectPaymentMethod": "Пожалуйста, выберите способ оплаты", "selectPaymentWay": "Пожалуйста, выберите способ оплаты", "paySuccess": "Платеж успешен", "payAmount": "Фактическая выплаченная сумма", "selectPayCountry": "Выберите страну платежа", "immediatePayment": "Оплатить сейчас", "orderCompleted": "Заказ выполнен", "tryUsingDifferentKeyword": "Измените ключевое слово и повторите попытку.", "insufficientCrystals": "Недостаточно кристаллов", "exchangeSuccessful": "Погашение успешно", "termTips": "Пожалуйста, внимательно прочтите приведенные выше условия. Ваша учетная запись не может быть восстановлена ​​после ее закрытия, поэтому выбирайте внимательно.", "noMore": "Нет больше", "msgDetail": "Сообщение деталей", "noMoreMsg": "Уже нет сообщений", "noMoreContent": "Уже нет контента", "invitationLink": "Пригласить ссылку", "loadingFailed": "SDK Загрузка не удалась", "tagEmptyTips": "Пожалуйста, выберите этикетку", "permissionRequire": "Заявка разрешения", "intimacyUnlockFunc": "Улучшите эксклюзивные функции разблокировать благоприятность", "syncPhoneCallRecord": "Записи голосового звонка были синхронизированы.", "payedOrder": "Заказ оплачен", "refundOrder": "Заказ возвращен.", "canceledOrder": "Заказ отменен", "pullToRefresh": "Потяните вниз, чтобы обновить", "looseToRefresh": "Отпустите, чтобы обновить", "confirmExitStory": "Вы уверены, что хотите выйти из текущей истории?", "savePicture": "Сохранить изображение", "interactWithHer": "Взаимодействовать с ней", "scanQRCode": "Отсканируйте QR-код, чтобы сыграть снова", "newerStoryName": "Возрождение: Побег из Лили", "flowerList": "Подарки", "popularityChart": "Популярность", "invalidSession": "Недействительная сессия, пожалуйста, войдите снова", "pressAgain": "Нажмите еще раз, чтобы выйти из приложения", "searchAgentRoles": "Искать персонажей", "day": "дни", "encounteredAlready": "Встречаюсь с Ugenie уже X дней", "latestVersion": "На данный момент последняя версия", "seeMore": "Посмотреть больше", "purchaseCancelled": "Покупка отменена", "buyFail": "Покупка не удалась", "unableConnect": "Невозможно подключиться к магазину", "unableFindProduct": "Товар не существует", "refuseGenerate": "Нет, спасибо", "inputFeedback": "Пожалуйста, введите отзыв", "thankFeedback": "Спасибо за ваш отзыв", "fillInFeedback": "Оставить отзыв", "report": "жалоба", "storyIntroduction": "Введение", "missionObjectives": "Цель", "missionRewards": "Награда", "rewardCongratulation": "История завершена, поздравляем с получением ", "generateCard": "Получить карточку истории", "backToChat": "Вернуться в чат", "playAgain": "Играть снова", "myStoryBook": "Моя книга", "moreStories": "Больше историй", "recheckStoryRecord": "Вы можете просмотреть это снова в разделе «История» на «Панели инструментов страницы диалога»", "storyBookGenerateConfirm": "Хотите создать книгу?", "storyBookGenerateTips": "Повторное прохождение удалит текущие записи истории. Хотите создать книгу, чтобы сохранить эту запись?", "agentProfile": "Профиль", "moreIntimacyToUnlock": "Повышайте уровень близости, чтобы разблокировать больше сюжетного контента", "story": "История", "saveInfomation": "Готово", "generateSuccessfully": "Успешно создано", "generateStoryBook": "Создать", "freeFirst": "Первый бесплатно", "goToChat": "Перейти в чат", "Completed": "Завершено", "Locked": "Заблокировано", "syncGameMemory": "Эксклюзивная память игрового процесса Lv{level} синхронизирована", "clickToStory": "Щелкните по карте, чтобы открыть эксклюзивную историю", "upgradeToUnlockStory": "Повышайте уровень близости с агентом, чтобы разблокировать", "copied": "Скопировано", "startNow": "Начать сейчас", "leaderboard": "Таблица лидеров", "featureCoding": "Функция скоро будет", "unableToSend": "Отправка недействительна", "getSuccessfully": "Получить успех", "freeInput": "Свободный ввод...", "adsToCrystal": "Посмотрите рекламу, чтобы получить 50 кристаллов", "selectOne": "Пожалуйста, выберите один", "shareContent": "Поделись агентом со своим другом!", "gameWin": "Победа в игре", "gameLose": "Провал в игре", "doubleRewards": "Двойные награды", "unlockByAds": "Разблокировать, просматривая рекламу", "rewardTip": "Поздравляем с получением награды", "feedback1": "Ошибка контента или неполный ответ", "feedback2": "Деликатный или порнографический контент", "feedback3": "Информация, связанная с насилием или ненавистью", "feedback4": "Неподходящая информация, касающаяся несовершеннолетних", "feedback5": "Беспокоящий контент", "inputFeedbackMessage": "Пожалуйста, введите проблему, с которой вы столкнулись", "submit": "Отправить", "feedbackSuccess": "Отправка успешна, спасибо за отзыв"}