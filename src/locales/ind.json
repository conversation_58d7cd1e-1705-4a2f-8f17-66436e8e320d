{"welcomeToEros": "Welcome to Ugenie!", "termsConfirmation": "Please confirm the following terms before using this site.", "ageConfirmation": "I am 18 years old or above.", "aiContentNotice": "I already know that the AI-related content on this site is fictional.", "registrationLogin": "Register/Login", "guestLogin": "Guest Login", "language": "Language", "emailLogin": "Email login", "email": "Email", "captchaInput": "Verification Code", "getCaptcha": "Obtain", "emailLoginOption": "<PERSON><PERSON>", "agreeToTerms": "Agreeing to the Privacy Policy and User Agreement upon logging in", "loginIssueContact": "Can't log in? Contact Customer Service", "enterEmail": "Please enter your email", "enterCaptcha": "Please enter the verification code", "invalidEmailFormat": "The email format is incorrect, please re-enter", "captchaError": "The verification code is incorrect, please re-enter", "captchaExpired": "The verification code has expired, please re-enter", "accountSuspendedNotice": "Your account has been suspended, for more details, you can check the Help Center for reasons or contact customer service", "selectNumber": "Choose your number", "skipOption": "<PERSON><PERSON>", "genderInterest": "Gender you like", "interests": "Interest", "allOptions": "All", "confirmSelection": "Confirm", "aiFilter": "Intelligent entity screening", "sortOptions": "Sorting method", "aiType": "Type of intelligent entity", "mostPopular": "Hottest", "latest": "Newest", "trending": "Popular", "searchRoleOrCreator": "Search for characters or creators", "searchHistory": "Search history", "popularRoles": "Popular characters", "noResultsFeedback": "Can't find what you want? Click feedback", "emptySearchSuggestion": "It's empty here, try another keyword?", "creatorSelection": "Creator", "chatNow": "Cha<PERSON>", "followedNotice": "Followed", "discoverUpdate": "New version found!", "updateNow": "Update now", "updateLater": "Do not update yet", "checkForUpdates": "Check for updates", "dynamicInteraction": "Experience dynamic interaction with characters, making communication more vivid and interesting", "swipeToClear": "Swipe left to enter 3D immersion mode", "triggerActions": "Click on special areas to trigger actions!", "chatBasedOnPersona": "Characters will chat with you based on your character setup, choose your character setup card to make her/him understand you better", "aiContentDisclaimer": "<PERSON><PERSON>-kata yang diucapkan oleh AI semuanya fiktif, harap berhati-hati", "changeTopicSuggestion": "Please change the topic", "makeCall": "Panggil", "giftItems": "<PERSON><PERSON>", "startNewChat": "<PERSON><PERSON>", "userCharacterOption": "My character setup", "startTyping": "Start typing", "resetCharacterNotice": "<PERSON><PERSON><PERSON> memu<PERSON>, ri<PERSON>at o<PERSON>lan <PERSON>a dengan karakter akan di<PERSON>pus", "cancelButton": "<PERSON><PERSON>", "cancelCreate": "Cancel creation", "newChatStartedNotice": "A new conversation has started, new messages are not affected above", "emptyContentVoicePlayback": "You have started a new topic", "recognitionFailedRetry": "Content is empty, cannot play voice", "unclearAudioRepeat": "Recognition failed, please retry", "voiceNotHeardNotice": "I didn't catch that, can you say it again?", "continueConversation": "The character cannot hear your voice and will automatically end the call after the countdown", "exitOption": "Continue the conversation", "clearAudioEnvironment": "Please try to keep a clean acoustic environment during the call", "purchaseCallDuration": "Deposit and get more character speaking time", "hangUp": "Hang up", "microphoneOn": "Microphone on", "showKeyboard": "Pull up the keyboard", "purchaseDuration": "Call time", "roleSpeakingDuration": "Only character speech consumes this duration", "remainingDuration": "Call time remains", "exchangeDuration": "Get more call time", "callDuration": "Call duration", "repeatedClickNotice": "You have repeatedly clicked, please try again later", "membershipBenefits": "Become a member to get more crystals", "unlockSelfie": "Unlock selfie", "totalCrystalUsed": "Accumulated crystal consumption", "confirmButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "insufficientBalance": "Insufficient balance, please deposit", "currentLevel": "Level saat ini", "increaseIntimacy": "Tingkatkan keintiman untuk membuka lebih banyak gameplay", "intimacyLevelUp": "Intimacy level up", "giftReceivedNotice": "Claimed yours", "giftBoostsIntimacy": "<PERSON><PERSON> hadiah dapat mening<PERSON>kan keintiman ~", "sendGift": "<PERSON><PERSON> had<PERSON>", "itemPurchase": "Purchase items", "unlockedFeatures": "Not unlocked", "purchaseOutfit": "Purchase outfit", "unlock3DOutfit": "Unlock 3D outfit", "setAsChatBackground": "Set as chat background", "exitImmersiveMode": "Exit immersion mode", "previousChats": "Chatted", "myCreations": "My Creations", "loginToView": "Log in to view", "emptyDiscover": "It's empty here, go to the Discovery page to check it out", "goDiscover": "Go to Discover", "createLover": "Create your own lover", "goCreate": "Go to Create", "followedYour": "Followed your", "followedYouNotice": "Followed you", "messageNotification": "Notification", "systemMessages": "System Messages", "interactiveMessages": "Interactive Messages", "reportFeedback": "Report Feedback", "deleteRole": "Delete Character", "aboutRole": "About TA", "photoAlbum": "Album", "outfits": "<PERSON><PERSON><PERSON><PERSON>", "characterSetting": "Character Design", "clickToSet": "Click to Set", "introduction": "Introduction", "editOption": "Edit", "openingLine": "Opening Remarks", "startChat": "Start Conversation", "retainCharacterNotice": "Do you want to keep your character?", "dataClearWarning": "All character data will be completely cleared, please proceed with caution", "improveAffection": "Improve favorability to unlock exclusive selfies", "receiveSelfiesHere": "Received selfies will be saved here", "selfieUnlocked": "Unlocked selfies", "saveToLocal": "Save to local", "staticOutfit": "Static Outfit", "unlock3DOption": "Unlock 3D", "unlockOutfitCost": "Unlocking an outfit consumes crystals", "clickToTouchSpace": "Enter to Touch Space", "creationMethod": "Creation Method", "quickCreate": "Quick Create", "selectGender": "Choose Character Gender", "maleOption": "Male", "femaleOption": "Female", "nonBinaryOption": "Non-Binary", "setPublic": "Set to Public", "publicSettingWarning": "If set to public, it cannot be made private", "personality": "Personality", "createAndStartChat": "Create and Start Chatting", "advancedCreation": "Advanced Creation", "customizeImage": "Custom Imagery", "freeImage": "Free Imagery", "enterNickname": "Enter Nickname", "completeBackground": "Complete the Backstory", "createCharacter": "Create Character", "creatingCharacter": "Creating", "regenerateOption": "Regenerate", "cropAvatar": "Crop Avatar", "continueCurrentSetting": "Continue with Current Settings?", "modifySettingNotice": "You can modify the settings and create a new imagery, but it will consume extra crystals", "savedInDrafts": "Generated images will be saved in the draft box", "exitAICharacterCreation": "Exit AI Entity Creation", "draftsFolder": "Draft Box", "useImage": "Use Imagery", "voiceOption": "Voice", "tag": "tag", "presetSoundLibrary": "Preset Voice Library", "characterSettingRequired": "Character Setup (required)", "openingLineRequired": "Opening Line (required)", "backgroundStoryRequired": "Backstory (required)", "characterIntroRequired": "Character Introduction (required)", "dialogueExampleOptional": "Dialogue Example (optional)", "tagRequired": "Label (required)", "clickToAddUserContent": "Click to Add User Dialogue Content", "clickToAddAIResponses": "Click to Add AI Response Content", "customizeImageName": "Detailed Configuration of Customized Image Name", "fansOption": "Fans", "likesOption": "<PERSON>s", "followOption": "Follow", "upgradePlanBenefits": "Subscription Plan", "activateNow": "Upgrade", "itemStore": "Item Mall", "imageOption": "Appearance", "characterOption": "Character", "noAIsNotice": "No AI entity yet, click the + below to summon an AI entity~", "myImageOption": "My Appearance", "addOption": "Add", "editImage": "Edit Appearance", "nicknameOption": "Nickname", "genderOption": "Gender", "birthdaySelection": "Birthday", "personalDescription": "Personal Description", "youRoleChatPersonalDesc": "Your character will chat with you based on your personal description, you can enter your preferences, things you hate, and other information~", "selectBirthday": "Choose <PERSON>", "saveButton": "Simpan", "unfollowOption": "Unfollow", "emptySpaceNotice": "It's Empty Here", "basicInfo": "My Profile", "languageSwitch": "Language", "helpFAQ": "Help FAQ", "defaultVoicePlayback": "De<PERSON><PERSON> Voice Playback", "feedbackOption": "Contact Us", "interestPreferences": "Interest Preferences", "inviteCodeEntry": "Invitation Code", "enterInviteCode": "Please Enter Invitation Code", "invalidInviteCode": "Invitation Code Does Not Exist", "rewardAlreadyClaimed": "Reward Already Claimed", "accountDeactivation": "Delete account", "deactivationTerms": "Cancellation Terms", "diamond": "diamond", "deactivationTermsContent1": "1.The account to be canceled is the currently logged-in account.", "deactivationTermsContent2": "2.Once canceled successfully, you will not be able to log in, reactivate, use, or recover this account again.", "deactivationTermsContent3": "3.If there are any unused memberships, coins, or diamonds in the account, they will be cleared upon successful cancellation.Please proceed with caution.", "deactivationTermsContent4": "4.After successful cancellation, your personal information and other data will be permanently deleted and cannot be recovered.", "deactivationTermsContent5": "5.After applying for cancellation, the account will be reserved for you for 15 days. If you log in during this period, the cancellation will be automatically canceled. If the cancellation is not revoked within 15 days, the account will be automatically canceled.", "deactivationTermsContent6": "请仔细阅读以上条款，账号注销后无法恢复，请谨慎选择。", "termsAgreement": "I have read and agree to the terms above", "nextStep": "Next Step", "accountVerification": "Account Verification", "otherVerificationOptions": "Other Verification Methods", "deactivationConfirmation": "Please read carefully and confirm again", "deactivationNotice1": "1. Once canceled successfully, your personal information and other data will be permanently deleted and cannot be recovered.", "deactivationNotice2": " 2. After applying for cancellation, the account will be reserved for you for 15 days. If you log in during this period, the cancellation will be automatically canceled. If the cancellation is not revoked within 15 days, the account will be automatically canceled.", "deactivationSuccessful": "Account Cancellation Application Successful", "verificationSuccess": "Verification Successful", "networkErrorRetry": "Network error, please try again later.", "accountVerificationFailed": "Account Verification Failed, <PERSON><PERSON> Account Invalid", "invalidEmailVerification": "The verification code is invalid, please try again later.", "captchaInvalidRetry": "Enable NSFW", "enableNSFWOption": "Current Version", "currentVersion": "Logout", "logoutOption": "Confirm <PERSON>ut", "confirmLogout": "After logging out, you will not be able to converse with the AI Entity, but you can still log in to this account.", "logoutInfo": "Member Shop", "orderHistory": "Not a Member Yet", "noMembershipActivated": "Enjoy VIP Benefits upon Registration", "activateVIPBenefits": "Buy Now", "purchaseNow": "Member Center", "membershipCenter": "Confirm Order Cancellation", "orderCancellationConfirm": "Note: Only used discounts (such as first-order discounts or vouchers) can be refunded upon order cancellation. If you have already made a payment, please do not cancel the order, as it may result in processing failures and cause you to incur losses.", "cancellationNotice": "The following discounts can be refunded for this order cancellation:", "cancellationRefundDetails": "Member First-Order Discount", "ensureNoPaymentBeforeCancel": "Please confirm you have not paid before canceling!", "closeButton": "Close", "cancelUnpaidOrder": "I haven't paid, cancel the order immediately", "orderRecords": "Order History", "membershipRecharge": "Subscription", "crystalPurchase": "Crystals", "crystalChangeDetails": "Crystal Change Details", "goldChangeDetails": "Gold Coin Change Details", "orderType": "Order Type", "creationTime": "Creation Time", "productName": "Product Name", "paymentMethod": "Payment Method", "actualPayment": "Amount <PERSON>", "processingOrder": "Processing Order", "closedOrder": "Closed", "cancelOrder": "Cancel Order", "changeTime": "Change Time", "records": "Record", "quantity": "Quantity", "superMembershipOffer": "Recharge Super Membership and Get Instant Rewards", "dailyGiftForSuperMembers": "Daily Gift for Super Members", "createCustomImage": "Create Custom Avatar", "createAI": "Create AI Entity", "purchaseSkin": "Buy Skin", "getSelfie": "Get a Selfie", "purchaseCallTime": "Buy Talk Time", "purchaseGift": "Buy Gift", "awaitingOrderCompletion": "Wait for Order Completion", "orderNumber": "Order Number", "paymentProcessing": "Processing Payment", "watiForPayment": "Due to payment channel issues, the payment may take 5-10 minutes to process. Please be patient.", "uncompleted": "Incomplete", "completed": "Completed", "return": "Back", "contactSupport": "If you have any issues, please contact customer service", "myCrystals": "My Crystals", "crystalDetails": "Crystal Details", "selectPurchaseItem": "Please select a product", "goldExchange": "Coin Exchange", "maxAmount": "Max", "exchangeOption": "Redeem", "cost": "Cost", "taskForFreeGold": "Complete tasks to earn more free coins", "task": "Missions/Tasks", "dailyTasks": "Daily Missions/Tasks", "specialTasks": "Special Missions/Tasks", "checkIn": "Check-in", "rewardReceivedNotice": "(Quantity) (Reward Type) has been credited", "rewardClaimed": "Claimed", "goComplete": "Go Complete", "claimReward": "<PERSON><PERSON><PERSON>", "notAchieved": "Not Achieved", "inviteFriends": "Invite Friends", "currentInvitedCount": "Currently Invited: 1/10", "store": "Store", "roleGifts": "<PERSON><PERSON>", "purchaseOption": "Purchase", "backpack": "Backpack", "searchAgentName": "Search for characters", "openingLineTips": "Please enter an opening remark", "choiceFigureTips": "Reselecting the avatar will not retain current settings", "selectFigureConfirm": "Reselect avatar?", "createAgain": "Recreate", "inputContentTips": "Please enter content (up to 300 characters)", "inputEmptyTips": "Input cannot be empty!", "sexChoice": "Gender selection", "audioEmpty": "No audio available", "template": "Template", "selectRequest": "Please select", "agentTemplateTips": "Please select an AI template", "abandonFigureConfirm": "Regenerating the avatar will cost crystals again, do you want to abandon the current avatar image?", "agentInfoTips": "Please complete the AI information", "backgroundStoryTips": "Please enter a background story", "characterIntroTips": "Please enter a brief introduction", "dialogueExample": "Dialogue examples", "agentFeatureTips": "Please complete the AI characteristics", "diyFigureSave": "Custom created avatars will be saved here", "cancelCreateTips": "Canceling creation will not retain your current settings", "cancelCreateConfirm": "Do you want to cancel the current creation?", "createAgentSuccess": "AI created successfully", "characterSetTips": "Please enter character settings", "noHaveAi": "The other party has no AI", "expand": "Expand", "collapse": "Collapse", "none": "None", "deleting": "Deleting", "deleteSuccessful": "Deletion successful", "confirmDeleteCharacter": "Are you sure you want to delete the character?", "download": "Download", "owned": "Owned", "notOwned": "Not owned", "noData": "No data available", "noCharacterGifts": "Nothing", "noCharacterSkins": "Nothing", "readAndConfirmed": "I have read and confirmed", "privacyAgreement": "Privacy Policy", "userAgreement": "User Agreement", "unableLogin": "Can't log in?", "contactCustomer": "Contact Customer Service", "loginAndAgree": "Sign in to agree", "noMoreOrder": "No Records", "noMoreCrystalDetails": "No crystal change details", "noMoreGoldDetails": "No gold coin change details", "goldDetails": "Gold coin details", "haveAnyQuestions": "If you have any questions, please", "taskAcquisition": "Task to obtain", "moreFreeCoins": "More free coins", "recordingPermissionError": "Tidak dapat memper<PERSON>h izin <PERSON>, silakan periksa pengat<PERSON>n", "recordingFailed": "Unable to record", "speechTooShort": "<PERSON><PERSON><PERSON> bicara terlalu singkat", "recordingError": "Recording failed", "waitForSpeaker": "<PERSON><PERSON> lain sedang mengetik...", "restarting": "<PERSON><PERSON><PERSON>", "restartSuccessful": "<PERSON><PERSON><PERSON> be<PERSON>", "likeCancelled": "Batalkan suka", "likeSuccessful": "<PERSON><PERSON>", "dislikeCancelled": "Batalkan tidak suka", "dislikeSuccessful": "Tidak suka berhasil", "conversationProcessing": "The agent is typing...", "voiceTooShort": "<PERSON><PERSON>tu suara terlalu singkat", "exitClearMode": "<PERSON><PERSON>ar dari mode layar bersih", "like": "<PERSON><PERSON>", "dislike": "Tidak suka", "copy": "<PERSON><PERSON>", "releaseToCancelSend": "Lepaskan untuk membatalkan pengiriman", "releaseToSendSwipeUpCancel": "Lepaskan untuk mengirim, geser ke atas untuk membatalkan", "holdToSpeak": "<PERSON><PERSON> untuk berb<PERSON>", "inUse": "In use", "rechargeSuccessful": "Recharge successful", "minutes": "Minutes", "exit": "Exit", "levelLocked": "Level yang tidak terkunci", "levelUnlocked": "Level yang tidak terkunci", "modelLoading": "Memuat model", "waitToSend": "Please wait for the other party to finish speaking before sending", "connecting": "Connecting", "microphoneOff": "Microphone off", "weeklyPurchaseLimit": "Batas pembelian minggu ini telah tercapai", "dailyPurchaseLimit": "Batas pembelian hari ini telah tercapai", "purchaseSuccessful": "Pembelian ber<PERSON>il", "totalCoinsSpent": "Accumulated consumption of gold coins", "free": "Free", "rechargeToUnlock": "Obtain by recharge", "moreCharacterTime": "More character speaking time", "micPermissionRequired": "<PERSON><PERSON><PERSON> per<PERSON> Anda untuk izin berikut agar dapat menggunakan mikrofon secara normal", "loading": "Memuat", "sendingGift": "<PERSON><PERSON> hadiah", "giftSent": "<PERSON><PERSON><PERSON>", "settingUp": "Mengganti latar belakang", "setupSuccessful": "Ditetapkan dengan sukses", "picture": "Picture", "message": "Message", "noMoreImage": "No more character settings for now", "editFigure": "Edit character settings", "profile": "Personal profile", "choosePreference": "Choose your preference", "recommend": "Recommended", "welcomeToUgenie": "Welcome to Ugenie", "CheckTerms": "Please check the terms", "abandonAccountCancellation": "Give up account cancellation", "abandonCancellation": "Give up cancellation", "discardLogoutPrompt": "Your account has submitted a cancellation request and will be successfully canceled at [时间]. Before the cancellation is successful, if you need to restore the account, please select \"Cancel Cancellation\" to log in to the account again, and your request will be canceled. If you do not need to cancel the cancellation, you can click \"Log in with another account.\"", "otherLoginWay": "Log in with another account", "aiOnTheWay": "The AI is still on its way～", "focusonSuccess": "Followed successfully~", "unfollowed": "Unfollowed", "fansAmount": "xxx followers", "rolesAmount": "xxx characters", "searchWant": "Search for what you want...", "search": "Search", "searchWantTips": "Can't find what you want?", "clickFeedback": "Get Support", "confirmDelFigure": "Are you sure you want to delete this avatar?", "verify": "Verify", "emailFormatError": "Incorrect email format", "inconsistentAccounts": "The verified account is not the currently logged-in account", "personalDescriptionTips": "Your character will be based on your personal description and your chats, you can input your likes, dislikes, and other information～", "successfullySaved": "Saved", "uploadPictures": "Uploading picture", "setup": "Settings", "remainingDiamonds": "Total Crystals", "exchangeCoins": "Exchange gold coins", "redeemNow": "Exchange now", "consumption": "Consumption", "weeklyTasks": "Weekly tasks", "gold": "Gold coins", "crystal": "Crystals", "inviteCode": "Invitation code", "inviteAmount": "Have invited xx people in total", "chooseCombo": "Please select a membership plan", "NonMember": "No membership is active at the moment", "cashRegister": "Cashier", "selectPaymentMethod": "Please select payment method", "selectPaymentWay": "Select payment method", "paySuccess": "Payment successful", "payAmount": "Actual amount paid", "selectPayCountry": "Select payment country", "immediatePayment": "Pay now", "orderCompleted": "Order completed", "tryUsingDifferentKeyword": "Try again with another keyword", "insufficientCrystals": "Insufficient crystals", "exchangeSuccessful": "Exchange successful", "termTips": "Please read the above terms carefully. Account cannot be restored after cancellation. Please choose carefully.", "noMore": "No more", "msgDetail": "Message details", "noMoreMsg": "No more messages", "noMoreContent": "No more content", "invitationLink": "Invite link", "loadingFailed": "SDK loading failed", "tagEmptyTips": "Please select the label", "permissionRequire": "<PERSON><PERSON><PERSON><PERSON>", "intimacyUnlockFunc": "Tingkatkan kesukaan untuk membuka fitur eksklusif", "syncPhoneCallRecord": "Voice call records have been synchronized.", "confirmExitStory": "Are you sure you want to exit the current story?", "savePicture": "Simpan gambar", "interactWithHer": "Interact with her", "scanQRCode": "Pindai kode QR untuk bermain lagi", "newerStoryName": "Rebirth: Escape from Lily", "payedOrder": "Order paid", "refundOrder": "Order refunded", "canceledOrder": "Order canceled", "pullToRefresh": "Pull down to refresh", "looseToRefresh": "Release to refresh", "flowerList": "Gift", "popularityChart": "Popularity", "invalidSession": "Invalid session, please log in again", "pressAgain": "Press again to exit the app", "searchAgentRoles": "Search for characters", "day": "days", "encounteredAlready": "Met Ugenie for X days", "latestVersion": "Currently the latest version", "seeMore": "View more", "purchaseCancelled": "Purchase canceled", "buyFail": "Purchase failed", "unableConnect": "Unable to connect to store", "unableFindProduct": "Product does not exist", "refuseGenerate": "No, thanks", "inputFeedback": "Please enter your feedback", "thankFeedback": "Thank you for your feedback", "fillInFeedback": "Fill out feedback", "report": "Report", "storyIntroduction": "Introduction", "missionObjectives": "Goal", "missionRewards": "<PERSON><PERSON>", "rewardCongratulation": "Story completed, congratulations on receiving ", "generateCard": "Collect the card", "backToChat": "Back to chat", "playAgain": "Play again", "myStoryBook": "My storybook", "moreStories": "More stories", "recheckStoryRecord": "You can view it again in the \"\" section of the \"Dialogue Page Toolbar\"", "storyBookGenerateConfirm": "Would you like to create a storybook?", "storyBookGenerateTips": "Replaying will erase the current story record. Would you like to create a storybook to save this record?", "restartStory": "Restarting will erase your current story progress. Are you sure to restart?", "agentProfile": "Profile", "moreIntimacyToUnlock": "Increase intimacy to unlock more story content", "story": "Cerita", "saveInfomation": "Done", "generateSuccessfully": "Generated successfully", "generateStoryBook": "Generate", "freeFirst": "First free", "goToChat": "Go to chat", "Completed": "Completed", "Locked": "Locked", "syncGameMemory": "Lv{level} Exclusive Gameplay Memory has been synchronized.", "clickToStory": "Klik kartu untuk memulai cerita eksklusif", "upgradeToUnlockStory": "Tingkatkan kesukaan untuk membuka kunci", "copied": "<PERSON>pied", "startNow": "<PERSON><PERSON>", "leaderboard": "Leaderboard", "send": "Give", "ugPhoneToLivco": "New Year, New Friends.", "saveSuccessfully": "Save successfully", "saveFailed": "Failed to save", "maturity": "expire", "featureCoding": "Fitur sedang dalam pengembangan", "unableToSend": "<PERSON><PERSON> t<PERSON> valid", "getSuccessfully": "Dapatkan sukses", "freeInput": "<PERSON><PERSON><PERSON> be<PERSON>...", "adsToCrystal": "Tonton iklan untuk mendapatkan 50 kristal", "selectOne": "<PERSON><PERSON>an pilih salah satu", "shareContent": "Bagikan Agent ke teman Anda!", "gameWin": "Kemenangan permainan", "gameLose": "Kegagalan permainan", "doubleRewards": "<PERSON><PERSON>", "unlockByAds": "<PERSON>uka kunci dengan menonton iklan", "rewardTip": "<PERSON><PERSON>at atas peneri<PERSON>an pen<PERSON>an", "feedback1": "Kesalahan konten atau respons tidak lengkap", "feedback2": "Ko<PERSON>n sensitif atau pornografi", "feedback3": "Informasi yang melibatkan kekerasan atau kebencian", "feedback4": "Informasi tidak pantas yang melibatkan anak di bawah umur", "feedback5": "<PERSON><PERSON>n yang mengganggu", "inputFeedbackMessage": "<PERSON><PERSON><PERSON> masukkan masalah yang Anda temui", "submit": "<PERSON><PERSON>", "feedbackSuccess": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, terima kasih atas masukan <PERSON>"}