import { createRouter, createWebHashHistory } from 'vue-router/auto'
import { existLanguages, locale } from '@/utils/i18n'
import useAppStore from '@/stores/modules/app'
import useLoadingStore from '@/stores/modules/loading'
// import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

import type { EnhancedRouteLocation } from './types'
import useRouteTransitionNameStore from '@/stores/modules/routeTransitionName'
import useRouteCacheStore from '@/stores/modules/routeCache'
import useUserStore from '@/stores/modules/user.ts'
import { ugPhoneLinkSdk } from '@/utils/ugPhoneLinkSdk.ts'
import { MQTT_LOCALSTORAGE_KEY } from '@/constant'
import { isEmpty } from 'lodash-es'
import useNotifyStore from '@/stores/modules/notification.ts'
import { TokenKey } from '@/utils/auth.ts'
const whiteList = ['/', '/login', '/agreement/privacy', '/agreement/user', '/setting/logoff']
const ugPhoneLinkList = ['/contentGame', '/agentGame', '/chat']

// NProgress.configure({ showSpinner: true, parent: '#app' })
const router = createRouter({
  history: createWebHashHistory(import.meta.env.VITE_APP_PUBLIC_PATH),
  extendRoutes: (routes) => routes
})
const previousRoutePath = ref('')
router.beforeEach(async (to: EnhancedRouteLocation, from, next) => {
  // NProgress.start()
  if (to.path.includes('/chat')) {
    const loading = useLoadingStore()
    loading.show()
  }
  const userStore = useUserStore()
  const appStore = useAppStore()
  const notifyStore = useNotifyStore()
  console.log(to)
  let hadUgphoneLink = false
  ugPhoneLinkList.forEach((item) => {
    if (to.path.toLowerCase().includes(item.toLowerCase())) {
      hadUgphoneLink = true
    }
  })
  if (hadUgphoneLink) {
    if (to.query.isLivco) {
      appStore.isUg = true
      const queryLang = to.query.lang as string
      localStorage.setItem('ug-lang', queryLang)
      const lang = existLanguages.includes(queryLang) ? queryLang : 'en'
      locale.value = lang
      appStore.language = lang
      localStorage.setItem('language', lang)
      const params = {
        small_id: to.query.small_id ? (to.query.small_id as string) : '',
        game_id: to.query.game_id ? (to.query.game_id as string) : '',
        timestamp: to.query.timestamp ? (to.query.timestamp as string) : '',
        user_id: to.query.user_id ? (to.query.user_id as string) : '',
        token: to.query.token ? (to.query.token as string) : '',
        screen_type: to.query.screen_type ? (to.query.screen_type as string) : '',
        lang: lang ? (lang as string) : '',
        ugphone_user_id: to.query.ugphone_user_id ? (to.query.ugphone_user_id as string) : '',
        ug_visitor_id: to.query.ug_visitor_id ? (to.query.ug_visitor_id as string) : ''
      }
      const data = await ugPhoneLinkSdk.login(params, to.query.key ? (to.query.key as string) : '')
      if (data) {
        userStore.saveLoginInfo(data)
        next()
        return
      } else {
        appStore.isUg = false
      }
    }
  }
  if (to.query.ugphone) {
    if (localStorage.getItem(TokenKey)) {
      next('/')
      return
    }
    sessionStorage.setItem('is_ugphone', 'ugphone')
  }
  if (!userStore.token && !whiteList.includes(to.path.toLowerCase())) {
    appStore.showLogin = true
    next('/')
    return
  }
  const routeCacheStore = useRouteCacheStore()
  const routeTransitionNameStore = useRouteTransitionNameStore()

  // Route cache
  routeCacheStore.addRoute(to)

  const sessionNotification = JSON.parse(sessionStorage.getItem('livco_notification'))
  const mqtt_config = JSON.parse(localStorage.getItem(MQTT_LOCALSTORAGE_KEY) ?? '{}')
  if (!isEmpty(mqtt_config) && !isEmpty(userStore.userInformation)) {
    if (!appStore.isUg) {
      console.log(2323232)
      notifyStore.initNoticeAndPoster(!sessionNotification)
    }
  }
  if (to.meta.level > from.meta.level) routeTransitionNameStore.setName('slide-fadein-left')
  else if (to.meta.level < from.meta.level) routeTransitionNameStore.setName('slide-fadein-right')
  else routeTransitionNameStore.setName('')
  previousRoutePath.value = from.fullPath
  next()
})

router.afterEach(() => {
  // NProgress.done()
  const loading = useLoadingStore()
  // 模拟延迟消除 loading（避免闪一下）
  loading.hide()
})

export default router
export { previousRoutePath }
