import { ICurrencyTypeEnum, RecordEnum } from '@/enums'
import { IGiftListType } from '@/api/agentChat/types.ts'

export interface ReportType {
  ai_id: number
  message: string
  type: number[]
}

export interface IChatRecordType {
  id: number
  output_text: string | IDialogueRecord[]
}

export type ButtonType = 'button_no_blocking_long' | 'button_no_blocking_short' | 'button_blocking'

export interface BtnContentType {
  ad: boolean
  callback?: string
  function?: 'restart' | 'share' | 'tools'
  content: string
  tools?: ToolContentType[]
}

// 原始对象类型定义
export interface DialogueObj {
  char_id: number
  content: string[]
  name?: string
}

export interface GlobalScoreObj {
  score_name: string
  change_score: number
  current_score: number
  total_score: number
}

export interface ScoreObj {
  score_name: string
  change_score: number
  current_score: number
  total_score: number
}

export interface NarrationNormalObj {
  content: string
}

export interface GameStatusObj {
  isShow: boolean
  explanation: string
  result: string
}

export interface ChapterObj {
  content: string
  tips: string
}

export interface GameInviteObj {
  invitation_content: string
  button_content: string
  callback?: string
  isShow: boolean
}

// 添加 attach_long_btn 类型映射
type WithBtn<T> = T & { button_no_blocking_long?: BtnContentType; button_blocking_short?: BtnContentType }

export interface IDialogueRecord {
  dialogue?: WithBtn<DialogueObj>
  global_score?: WithBtn<GlobalScoreObj[]>
  score?: WithBtn<ScoreObj>
  narration_normal?: WithBtn<NarrationNormalObj>
  game_status?: WithBtn<GameStatusObj>
  narration_chapter_title?: ChapterObj
  game_invitation?: WithBtn<GameInviteObj>
  short_tips?: WithBtn<{ content: string }>
  picture_url?: WithBtn<{ content: string }>
  picture_backend_id?: WithBtn<{ content: string; isShow: boolean }>
  video_backend_id?: WithBtn<{ content: string; isShow: boolean }>
  chat_lock?: WithBtn<{ isLock: boolean }>
  story_image?: WithBtn<{ content: string; content_id: number }>
}

export interface RecordType {
  timestamp?: number
  type: RecordEnum
  content: string
  buttonType?: ButtonType
  revert_content?: IDialogueRecord
  record_id?: number
  message_id?: string
  replayTime?: number
  // 一个数组可能有0,1,2个字符串元素
  // replayContent?: [string?, string?]
  is_like?: number
  is_bad?: number
  is_phone_call?: number
  is_later?: number
  ttsLoading?: boolean
  ttsPlaying?: boolean
  textLoading?: boolean
  userTTS?: boolean
  call_time?: number
  content_id?: number
  currency_type?: ICurrencyTypeEnum
  price?: number
  gift_info?: IGiftListType
}

export interface IChatResponseType {
  pjh_resp: {
    conversation_id: string
    frontend_answer: IDialogueRecord[]
    tts_answer: {
      char_id: string
      content: string
      voice_path: string
    }[]
    llm_emotion: string
    tools?: ToolContentType[]
  }
  double_get_crystal: number
  intimacy_interest_tags: string[]
  intimacy_level: number
  intimacy_now: number
  intimacy_upgrade: number
  record_id: number
  adv_info?: {
    type: number
  }
}

export interface ToolContentType {
  tool_type: string
  tool_content: string
}

export interface ChatRequestType {
  ai_id: number
  query: string
  pjh_query: string | object
  interactive_model?: number
  conversation_id?: string
  response_way?: string
  gift_id?: number
  gift_count?: number
  volume: number
}

export type AdvInfoType = {
  [key in AdvType]: {
    status: number
    type: 1 | 2 // 1:插页广告 2:激励广告
  }
}

export type AdvType =
  | 'chat_times'
  | 'double_get_crystal'
  | 'get_crystal'
  | 'input_short_option'
  | 'long_option'
  | 'quit_chat'
  | 'start_history'
  | 'sure_content_invite'
  | 'unlock_image'
