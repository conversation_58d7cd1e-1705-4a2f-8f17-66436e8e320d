import service from '@/utils/service.ts'
import {
  AgentChatResType,
  AgentPhoneTextChatType,
  AgentSendGiftType,
  AgentTextChatType,
  BlockChatResponseType,
  GiftListResType,
  LikeOrUnlikeType,
  PhoneCallOpenReq,
  PhoneCallTimeGoodsType,
  SelfieType,
  ThreeDSourceType
} from '@/api/agentChat/types.ts'
import { INormalList, IRequestPageNormalParams } from '@/types'
import { PerSetVoiceEnum } from '@/enums'
import { DressType, SkinTypeEnum } from '@/api/propertyStore/types.ts'

export const getChatRecord = (data: IRequestPageNormalParams & { ai_id: number }) => {
  return service.post<INormalList<AgentChatResType>>({
    url: '/apiv1/chat/chat_record',
    data
  })
}

export const startNewChat = (data: { ai_id: number }) => {
  return service.post({
    url: '/apiv1/chat/start_new_v2',
    data
  })
}

export const agentTextChat = (data: AgentTextChatType) => {
  return service.stream({
    url: '/apiv1/chat/chat',
    data: {
      type: 1,
      is_phone_call: 0,
      ...data
    }
  })
}

export const agentPhoneCallTextChat = (data: AgentPhoneTextChatType) => {
  return service.stream({
    url: '/apiv1/chat/chat',
    data: {
      type: 1,
      is_phone_call: 1,
      ...data
    }
  })
}

export const agentAudioChat = (data: FormData) => {
  return service.stream({
    url: '/apiv1/chat/chat',
    data
  })
}

export const ttsMessage = (data: { message_id: string }) => {
  return service.stream({
    url: '/apiv1/chat/tts',
    data
  })
}

export const ttsMessageText = (data: { text: string; ai_id: number }) => {
  return service.stream({
    url: '/apiv1/chat/tts_text',
    data
  })
}

export const likeOrUnlikeAgentText = (data: LikeOrUnlikeType) => {
  return service.post({
    url: '/apiv1/chat/like_or_bad',
    data
  })
}

export const getGift = (data: IRequestPageNormalParams) => {
  return service.post<INormalList<GiftListResType>>({
    url: '/apiv1/info/goods_list',
    data
  })
}

export const sendGift = (data: AgentSendGiftType) => {
  return service.stream({
    url: '/apiv1/chat/chat',
    data: {
      type: 3,
      ...data
    }
  })
}

export const getPhoneCallOpenVoice = (data: PhoneCallOpenReq) => {
  return service.stream({
    url: '/apiv1/chat/chat',
    data: {
      type: 4,
      is_phone_call: 1,
      ...data
    }
  })
}

export const getDressByID = (data: { ai_id: number }) => {
  return service.post<DressType[]>({
    url: '/apiv1/dress/getDressByAi',
    data
  })
}

export const setDress = (data: { set_id: number; ai_id?: number }) => {
  return service.post({
    url: '/apiv1/dress/setDress',
    data
  })
}

export const getDressDetail = (data: { id: number; type: SkinTypeEnum }) => {
  return service.post<DressType>({
    url: '/apiv1/dress/getDressDetail',
    data
  })
}

export const getPresetVoice = (data: { ai_id: number; preset_voice: PerSetVoiceEnum }) => {
  return service.stream({
    url: '/apiv1/chat/preset_tts',
    data
  })
}

export const getSelfie = (data: { ai_id: number }) => {
  return service.post<SelfieType>({
    url: '/apiv1/chat/get_a_selfie',
    data
  })
}

export const getThreeDSource = (data: { id: number }) => {
  return service.post<ThreeDSourceType>({
    url: '/apiv1/dress/getModelJson',
    data
  })
}

export const getCrystalToPhoneTime = () => {
  return service.get<number>({
    url: '/apiv1/wallet/crystal_to_phone_time'
  })
}

export const getPhoneStoreList = () => {
  return service.post<INormalList<PhoneCallTimeGoodsType>>({
    url: '/apiv1/wallet/pt_goods_list'
  })
}

export const setImage = (data: { ai_id: number; image_id: number }) => {
  return service.post({
    url: '/apiv1/ai/set_image',
    data
  })
}

export const getIntimacyStrategy = () => {
  return service.post<string>({
    url: '/apiv1/info/intimacy_strategy_guide'
  })
}

export const bgmSetting = (data: { bgm: number }) => {
  return service.post({
    url: '/apiv1/user/bgm_setting',
    data
  })
}

export const blockAgentTextChat = (data: AgentTextChatType) => {
  return service.post<BlockChatResponseType>({
    url: '/apiv1/chat/chat_v2',
    data
  })
}

export const blockAgentAudio = (data: FormData) => {
  return service.post<string>({
    url: '/apiv1/chat/audio_to_text',
    data
  })
}

export const webSearchTool = (data: { ai_id: number; tool_type: string; tool_content: string }) => {
  return service.post<BlockChatResponseType>({
    url: '/apiv1/chat/tool_v2',
    data
  })
}

export const newBlockAgentTextChat = (data: AgentTextChatType) => {
  return service.post<BlockChatResponseType>({
    url: '/apiv1/chat/chat_v3',
    data
  })
}
