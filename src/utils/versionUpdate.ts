export function compareVersions(version1: string, version2: string) {
  console.log('version1', version1, 'version2', version2)
  // 将版本号字符串以点分割成数组，并补全较短版本号数组的尾部为0
  const v1Parts = version1.split('.').map(Number)
  const v2Parts = version2.split('.').map(Number)

  // 获取最长的数组长度，用于补全较短的版本号数组
  const maxLength = Math.max(v1Parts.length, v2Parts.length)

  // 补全版本号数组到相同的长度，不足的部分用0填充
  while (v1Parts.length < maxLength) v1Parts.push(0)
  while (v2Parts.length < maxLength) v2Parts.push(0)

  // 比较版本号的每一部分
  for (let i = 0; i < maxLength; i++) {
    if (v1Parts[i] > v2Parts[i]) {
      return false // version1 大于 version2，无需更新
    } else if (v1Parts[i] < v2Parts[i]) {
      return true // version1 小于 version2，需要更新
    }
  }

  // 如果所有部分都相同，则不需要更新
  return false
}
