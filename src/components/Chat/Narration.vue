<script setup lang="ts">
import { RecordType } from '@/api/chat/types.ts'

const props = withDefaults(
  defineProps<{
    record: RecordType
    index: number
  }>(),
  {}
)
</script>

<template>
  <div class="narration-normal">
    <img
      src="@/assets/images/chat/normal-narraion-decoration-top.png"
      alt=""
    />
    <img
      src="@/assets/images/chat/normal-narraion-decoration-bottom.png"
      alt=""
    />{{ props.record.content }}
  </div>
</template>

<style scoped lang="scss">
.narration-normal {
  position: relative;
  padding: 10px 16px;
  margin: 8px 0;
  font-size: 12px;
  line-height: 16px;
  color: rgba(255, 255, 255, 88%);
  text-align: center;
  white-space: pre-wrap;
  background: linear-gradient(
    90deg,
    rgba(13, 13, 13, 0.1) 0%,
    rgba(13, 13, 13, 0.6) 15%,
    rgba(13, 13, 13, 0.9) 50%,
    rgba(13, 13, 13, 0.6) 85%,
    rgba(13, 13, 13, 0.1) 100%
  );
  border: 1px solid;
  border-image: linear-gradient(90deg, rgba(255, 255, 255, 0%), rgba(255, 255, 255, 50%), rgba(255, 255, 255, 0%)) 1 1;
}

img {
  position: absolute;
  left: 50%;
  height: 12px;

  &:first-child {
    top: 0;
    transform: translate(-50%, -70%);
  }

  &:last-child {
    bottom: 0;
    transform: translate(-50%, 70%);
  }
}
</style>
