<script setup lang="ts">
import { RecordType } from '@/api/chat/types.ts'
import { markdownToHtml, replaceBracketsWithAsterisks } from '@/utils'

const props = withDefaults(
  defineProps<{
    record: RecordType
  }>(),
  {}
)
</script>

<template>
  <div v-html="markdownToHtml(replaceBracketsWithAsterisks(props.record.content))"></div>
</template>
<style>
.markdown-image {
  display: none;
  max-width: 75%;
  min-width: 20%;
  max-height: 40vh;
  border-radius: 12px;
}

.image-placeholder {
  width: 75%;
  aspect-ratio: 2/1;
  border-radius: 12px;
}
</style>
<style scoped lang="scss"></style>
