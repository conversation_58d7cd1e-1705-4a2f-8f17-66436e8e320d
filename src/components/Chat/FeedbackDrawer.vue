<script setup lang="ts">
import { useModal } from '@/hooks/useModal.ts'
import { reportAI } from '@/api/chat'
const { t } = useI18n()

const show = defineModel<boolean>()
const options = ref([])
const message = ref('')
const aiID = inject<Ref<number>>('aiID')

const submit = () => {
  reportAI({
    ai_id: aiID?.value,
    message: message.value,
    type: options.value
  }).then(
    (res) => {
      if (res.code === 200) {
        useModal({
          message: t('feedbackSuccess')
        })
      }
    },
    (err) => {
      console.warn(err)
    }
  )
  show.value = false
}

watch(
  show,
  (value) => {
    if (!value) {
      options.value = []
      message.value = ''
    }
  },
  {
    immediate: true
  }
)
</script>

<template>
  <van-popup
    v-model:show="show"
    position="bottom"
    :style="{
      background: '#232222',
      'border-radius': '24px 24px 0px 0px',
      padding: '32px 24px 24px',
      maxHeight: '80vh'
    }"
  >
    <div>
      <van-checkbox-group
        class="checkbox-group"
        v-model="options"
      >
        <van-checkbox
          name="a"
          shape="square"
        >
          {{ t('feedback1') }}
        </van-checkbox>
        <van-checkbox
          name="b"
          shape="square"
        >
          {{ t('feedback2') }}
        </van-checkbox>
        <van-checkbox
          name="c"
          shape="square"
        >
          {{ t('feedback3') }}
        </van-checkbox>
        <van-checkbox
          name="d"
          shape="square"
        >
          {{ t('feedback4') }}
        </van-checkbox>
        <van-checkbox
          name="e"
          shape="square"
        >
          {{ t('feedback5') }}
        </van-checkbox>
      </van-checkbox-group>
      <div class="mt-24 mb-24 input">
        <van-field
          v-model="message"
          type="textarea"
          :placeholder="t('inputFeedbackMessage')"
        />
      </div>
      <div class="flex-center-center">
        <van-button
          class="submit-btn"
          @click="submit"
          :disabled="!options.length"
        >
          {{ t('submit') }}
        </van-button>
      </div>
    </div>
  </van-popup>
</template>

<style scoped lang="scss">
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  background: #ffffff15;
  border-radius: 12px;
}

.input {
  :deep(.van-field) {
    background: rgba(255, 1255, 255, 15%);
    border: 1px solid rgba(223, 219, 255, 20%);
    border-radius: 16px;

    textarea::placeholder {
      color: #ffffff80;
    }
  }
}

.submit-btn {
  width: 100%;
  height: 42px;
  font-size: 16px;
  line-height: 42px;
  color: #000;
  text-align: center;
  background: $livCoThemeColor;
  border: 1px solid rgba(223, 219, 255, 20%);
  border-radius: 12px;
}
</style>
