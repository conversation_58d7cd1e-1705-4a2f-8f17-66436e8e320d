<script setup lang="ts">
import { useWebSocket } from '@vueuse/core'
import { showNotify } from 'vant'
import Recorder from 'recorder-core/recorder.mp3.min.js'
import { useCountDown } from '@vant/use'
import useUserStore from '@/stores/modules/user.ts'
import { agentAudioChat, agentPhoneCallTextChat, getPhoneCallOpenVoice } from '@/api/agentChat'
import { ResultEnum } from '@/enums/httpEnum.ts'
import { useModal } from '@/hooks/useModal.ts'
import { Ref } from 'vue'
import { AgentMessageType } from '@/api/agentHomePage/types.ts'
import { debounce } from 'lodash-es'
import { useHandleStream } from '@/hooks/useHandleStream.ts'
import { Howl } from 'howler'
import router from '@/router'
const { t } = useI18n()

const live2dStatus = inject<Ref<boolean>>('live2dStatus')
const userStore = useUserStore()
const emits = defineEmits(['getRecord'])

const CALL_TYPE = {
  CALL: 1,
  HANG_OUT: 2
}

const props = withDefaults(
  defineProps<{
    audioContext: AudioContext
  }>(),
  {}
)

const { send, open, close } = useWebSocket(import.meta.env.VITE_APP_WS_PATH, {
  immediate: false,
  onConnected() {
    send(
      JSON.stringify({
        client_id: userStore.userInformation.client_id,
        ai_id: aiID.value,
        type: 'phone_call',
        interactive_model: live2dStatus.value ? 2 : 1,
        call_type: CALL_TYPE.CALL
      })
    )
    console.log('connect success')
  },
  onMessage(_, event) {
    console.log(event.data)
    if (event.data === 'success') {
      emits('getRecord')
      close()
      myRecorder.value && myRecorder.value.close()
      myRecorder.value = null

      myStream.value && myStream.value.stop()
      myStream.value = null
      show.value = false
    } else if (
      !event.data.includes(
        JSON.stringify({
          type: 'ping'
        })
      )
    ) {
      let data
      try {
        data = JSON.parse(event.data)
      } catch (e) {
        console.warn(e)
      }
      phoneCallID.value = data.phone_call_id
      callStatus.connecting = false
      callStatus.thinking = true
      getPhoneCallOpenVoice({
        ai_id: aiID.value,
        phone_call_id: data.phone_call_id,
        interactive_model: live2dStatus.value ? 2 : 1
      })
        .then(async (response) => {
          question.value = ''
          useHandleStream(response, {
            onAudioData: (audioData) => {
              const hexString = audioData.data.audio
              if (audioData.data.status === 1) {
                console.log('success')
                const arrayBuffer = new ArrayBuffer(hexString.length / 2)
                // 创建一个新的Uint8Array视图，以便我们可以将数据写入ArrayBuffer
                const uint8Array = new Uint8Array(arrayBuffer)

                // 遍历十六进制字符串，每两个字符转换为一个字节
                for (let i = 0; i < hexString.length; i += 2) {
                  uint8Array[i / 2] = parseInt(hexString.slice(i, i + 2), 16)
                }
                callStatus.thinking = false
                callStatus.talking = true
                myStream.value.input(arrayBuffer)
              }
            },
            onCompleted: () => {},
            onData: (message) => {
              agentText.value += message
              scrollBottom()
            },
            onError: (e) => {
              console.warn(e)
            }
          })
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {})
    }
  },
  onError(_, event) {
    console.warn(event)
  }
})
const inputRef = ref(null)
const show = defineModel<boolean>()
const countdownVisible = ref(false)
const topUpVisible = ref(false)
const phoneCallID = ref()
const agentText = ref('')

const agentMessage = inject<Ref<AgentMessageType>>('agentMessage')

const noSoundCountdown = useCountDown({
  time: 60 * 1000,
  onFinish() {
    if (callStatus.talking) {
      overMinuteMute.value = false
      return
    }
    if (!callStatus.micOpening && !callStatus.thinking && !callStatus.talking) {
      console.log('finish,Show')
      countdownVisible.value = true
    } else {
      console.log('finish,isReact,overTrue')
      overMinuteMute.value = true
    }
  }
})
const myRecorder = ref(null)
const myStream = ref(null)
const agentTextRef = ref()
const howlInstance = ref(null)
const callStatus = reactive({
  connecting: true,
  opening_statement: true,
  micOpening: false,
  recording: false,
  thinking: false,
  talking: false,
  inputting: false
})

const muteNum = ref(0)
const SECOND_NUM = 12
const MUTE_LEVEL = 5
const hadSound = ref(false)
const overMinuteMute = ref(false)
const volumeNum = ref(0)

const question = ref('')

const aiID = inject<Ref<number>>('aiID')

const openKeyboard = () => {
  callStatus.inputting = true
  if (!callStatus.recording) {
    return
  }
  volumeNum.value = 0
  if (callStatus.micOpening) {
    recStop(true)
  }
  callStatus.micOpening = !callStatus.micOpening
  nextTick(() => {
    inputRef.value.focus()
  })
}

const hangOut = () => {
  send(
    JSON.stringify({
      client_id: userStore.userInformation.client_id,
      type: 'phone_call',
      interactive_model: live2dStatus.value ? 2 : 1,
      call_type: CALL_TYPE.HANG_OUT,
      phone_call_id: phoneCallID.value
    })
  )
}

const resetCountDown = () => {
  countdownVisible.value = false
  overMinuteMute.value = false
  noSoundCountdown.reset()
  noSoundCountdown.start()
}

const handleDot = () => {
  if (callStatus.thinking) {
    return 'dot-circle'
  } else if (callStatus.talking) {
    return 'dot-sound'
  } else {
    return 'dot'
  }
}

const shouldHangout = computed(() => {
  return !(callStatus.talking || callStatus.thinking)
})

const handleTopUp = () => {
  if (!callStatus.recording) {
  } else {
    console.log(callStatus)
    volumeNum.value = 0
    if (callStatus.micOpening) {
      recStop(false)
    }
  }
  callStatus.micOpening = false
  topUpVisible.value = true
  noSoundCountdown.reset()
}

const scrollBottom = () => {
  nextTick(() => {
    agentTextRef.value.scrollTop = agentTextRef.value.scrollHeight
  })
}

const recOpenInit = (success: typeof recStart) => {
  if (!myRecorder.value) {
    myRecorder.value = new Recorder({
      type: 'mp3',
      sampleRate: 16000,
      bitRate: 16,
      onProcess: function (_: any, powerLevel: number, bufferDuration: number) {
        console.log(powerLevel, bufferDuration)
        if (hadSound.value) {
          volumeNum.value = powerLevel
          if (powerLevel < MUTE_LEVEL) {
            muteNum.value += 1
          } else {
            muteNum.value = 0
          }
          if (muteNum.value >= SECOND_NUM * 2) {
            console.log('stop' + '上传音频')
            noSoundCountdown.reset()
            noSoundCountdown.start()
            recStop(false)
            // 重置所有状态
            volumeNum.value = 0
            muteNum.value = 0
            hadSound.value = false
          }
        } else {
          if (powerLevel > MUTE_LEVEL) {
            volumeNum.value = powerLevel
            hadSound.value = true
          } else {
            if (bufferDuration > 9000) {
              console.log('stop' + '触发没听清接口')
              recStop(false)
            }
          }
        }
      }
    })
  }
  myRecorder.value.open(
    function () {
      //打开麦克风授权获得相关资源
      //rec.start() 此处可以立即开始录音，但不建议这样编写，因为open是一个延迟漫长的操作，通过两次用户操作来分别调用open和start是推荐的最佳流程]
      success && success()
      callStatus.micOpening = true
    },
    function (msg: string, isUserNotAllow: boolean) {
      //用户拒绝未授权或不支持
      if (isUserNotAllow) {
        showNotify({
          type: 'danger',
          message: msg
        })
      }
      console.log((isUserNotAllow ? 'UserNotAllow，' : '') + '无法录音:' + msg)
    }
  )
}

const recStart = () => {
  callStatus.recording = true
  myRecorder.value.start()
}

/**
 *
 * @param isPause true:手动暂停 false:自动发送消息
 */
const recStop = (isPause: boolean) => {
  myRecorder.value?.stop(
    async function (blob: Blob, duration: number) {
      console.log(blob, '时长:' + duration + 'ms')
      // myRecorder.value.close() //释放录音资源，当然可以不释放，后面可以连续调用start；但不释放时系统或浏览器会一直提示在录音，最佳操作是录完就close掉
      // myRecorder.value = null
      if (duration < 1000) {
        // useModal({
        //   message: t('speechTooShort')
        // })
        return
      }
      console.log('hadSound', hadSound.value)
      if (!isPause) {
        callStatus.recording = false
        callStatus.thinking = true
        //已经拿到blob文件对象想干嘛就干嘛：立即播放、上传、下载保存
        const file = new File([blob], 'record.mp3')
        const formData = new FormData()
        formData.append('audio_file', file)
        formData.append('type', '2')
        formData.append('is_phone_call', '1')
        formData.append('phone_call_id', phoneCallID.value)
        formData.append('ai_id', String(aiID.value))
        formData.append('interactive_model', String(live2dStatus.value ? 2 : 1))
        console.log(formData)
        agentAudioChat(formData)
          .then(async (response) => {
            agentText.value = ''
            useHandleStream(response, {
              onAudioData: (audioData, isEffectiveAudio) => {
                if (isEffectiveAudio) {
                  overMinuteMute.value = false
                  noSoundCountdown.reset()
                }
                const hexString = audioData.data.audio
                if (audioData.data.status === 1) {
                  console.log('success')
                  const arrayBuffer = new ArrayBuffer(hexString.length / 2)
                  // 创建一个新的Uint8Array视图，以便我们可以将数据写入ArrayBuffer
                  const uint8Array = new Uint8Array(arrayBuffer)

                  // 遍历十六进制字符串，每两个字符转换为一个字节
                  for (let i = 0; i < hexString.length; i += 2) {
                    uint8Array[i / 2] = parseInt(hexString.slice(i, i + 2), 16)
                  }
                  callStatus.thinking = false
                  callStatus.talking = true
                  myStream.value.input(arrayBuffer)
                }
              },
              onPhoneTimeBalance: (phone_time_balance) => {
                userStore.userInformation.phone_time = phone_time_balance
              },
              onCompleted: () => {},
              onData: (message) => {
                agentText.value += message
                scrollBottom()
              },
              onError: (e) => {
                console.warn(e)
              },
              onNormalResponse: (code, data, msg) => {
                if (code === ResultEnum.AGENT_REMOVED) {
                  useModal({
                    message: msg,
                    duration: 1500,
                    onClose: () => {
                      router.push('/')
                    }
                  })
                  return
                }
                if (code === ResultEnum.TTS_ERROR) {
                  if (overMinuteMute.value) {
                    countdownVisible.value = true
                  }
                  useModal({
                    message: msg,
                    duration: 1500
                  })
                  callStatus.thinking = false
                  callStatus.recording = true
                  if (!myRecorder.value || callStatus.inputting || !callStatus.micOpening) return
                  recStart()
                }
                if (code === ResultEnum.OVER_PHONE_TIME) {
                  callStatus.thinking = false
                  handleTopUp()
                }
                if (code === ResultEnum.UNCLEAR_VOICE) {
                  howlInstance.value = new Howl({
                    src: data,
                    onplay: () => {
                      callStatus.thinking = false
                      callStatus.talking = true
                      console.log('Audio is playing.')
                    },
                    onstop: () => {
                      console.log('Audio has stopped.')
                    },
                    onload: () => {
                      howlInstance.value.play()
                      console.log('Audio has loaded.')
                    },
                    onend: () => {
                      console.log('Audio has ended.')
                      handleAudioEnd(false)
                    }
                  })
                }
              }
            })
          })
          .catch((err) => {
            console.log(err)
          })
          .finally(() => {})
      }
    },
    function (msg: string) {
      console.log('录音失败:' + msg)
    }
  )
}

const sendText = () => {
  if (!question.value) {
    return
  }
  if (callStatus.thinking || callStatus.talking) {
    useModal({
      message: t('waitForSpeaker'),
      duration: 1000
    })
    return
  }
  callStatus.thinking = true
  agentPhoneCallTextChat({
    query: question.value,
    ai_id: aiID.value,
    phone_call_id: phoneCallID.value,
    interactive_model: live2dStatus.value ? 2 : 1
  })
    .then(async (response) => {
      question.value = ''
      agentText.value = ''
      useHandleStream(response, {
        onAudioData: (audioData, isEffectiveAudio) => {
          if (isEffectiveAudio) {
            overMinuteMute.value = false
            noSoundCountdown.reset()
          }
          const hexString = audioData.data.audio
          if (audioData.data.status === 1) {
            console.log('success')
            const arrayBuffer = new ArrayBuffer(hexString.length / 2)
            // 创建一个新的Uint8Array视图，以便我们可以将数据写入ArrayBuffer
            const uint8Array = new Uint8Array(arrayBuffer)

            // 遍历十六进制字符串，每两个字符转换为一个字节
            for (let i = 0; i < hexString.length; i += 2) {
              uint8Array[i / 2] = parseInt(hexString.slice(i, i + 2), 16)
            }
            callStatus.thinking = false
            callStatus.talking = true
            myStream.value.input(arrayBuffer)
          }
        },
        onCompleted: () => {},
        onPhoneTimeBalance: (phone_time_balance) => {
          userStore.userInformation.phone_time = phone_time_balance
        },
        onData: (message) => {
          agentText.value += message
          scrollBottom()
        },
        onError: (e) => {
          console.warn(e)
        },
        onNormalResponse: (code, _, msg) => {
          if (code === ResultEnum.AGENT_REMOVED) {
            useModal({
              message: msg,
              duration: 1500,
              onClose: () => {
                router.push('/')
              }
            })
            return
          }
          if (code === ResultEnum.TTS_ERROR) {
            useModal({
              message: msg,
              duration: 1500
            })
            callStatus.thinking = false
            callStatus.recording = true
            if (!myRecorder.value || callStatus.inputting || !callStatus.micOpening) return
            recStart()
          }
          if (code === ResultEnum.OVER_PHONE_TIME) {
            callStatus.thinking = false
            handleTopUp()
          }
        }
      })
    })
    .catch((err) => {
      console.log(err)
    })
    .finally(() => {})
}

const pauseRestartRecording = () => {
  if (!callStatus.recording) {
    return
  }
  volumeNum.value = 0
  navigator?.vibrate?.(30)
  if (callStatus.micOpening) {
    recStop(false)
  } else {
    recStart()
  }
  callStatus.micOpening = !callStatus.micOpening
}

const handleAudioEnd = (isEffective: boolean) => {
  callStatus.talking = false
  callStatus.recording = true
  if (callStatus.opening_statement) {
    if (!callStatus.inputting) {
      recOpenInit(recStart)
      noSoundCountdown.start()
    }
    callStatus.opening_statement = false
    return
  }
  if (userStore.userInformation.phone_time <= 0 && isEffective) {
    console.log(121)
    handleTopUp()
    return
  }
  if (overMinuteMute.value) {
    console.log('playEnd,showCountDown')
    countdownVisible.value = true
    callStatus.micOpening = false
  } else {
    console.log('countdownStart')
    noSoundCountdown.start()
  }
  if (!myRecorder.value || callStatus.inputting || !callStatus.micOpening || countdownVisible.value) return
  recStart()
}

const debouncePauseRestart = debounce(pauseRestartRecording, 500, {
  leading: true,
  trailing: false
})

const audioStream = () => {
  myStream.value = Recorder.BufferStreamPlayer({
    play: true, //要播放声音，设为false不播放，只提供MediaStream
    realtime: false,
    //,onUpdateTime:fn() //已播放时长、总时长更新回调（stop、pause、resume后一定会回调），this.currentTime为已播放时长，this.duration为已输入的全部数据总时长（实时模式下意义不大，会比实际播放的长），单位都是ms
    onPlayEnd: () => {
      handleAudioEnd(true)
    }, //没有可播放的数据时回调（stop后一定会回调），已输入的数据已全部播放完了，可代表正在缓冲中或播放结束；之后如果继续input输入了新数据，播放完后会再次回调，因此会多次回调；非实时模式一次性输入了数据时，此回调相当于播放完成，可以stop掉，重新创建对象来input数据可达到循环播放效果
    decode: true, //input输入的数据在调用transform之前是否要进行一次音频解码成pcm [Int16,...]
    runningContext: props.audioContext
  })

  //创建好后第一件事就是start打开流，打开后就会开始播放input输入的音频；注意：start需要在用户操作(触摸、点击等)时进行调用，原因参考runningContext配置
  myStream.value.start(
    () => {
      console.log('audio play start')
    },
    (errMsg: string) => {
      console.warn(errMsg)
      //start失败，无法播放
    }
  )
}

watch(
  () => callStatus.inputting,
  (val) => {
    if (!val) {
      callStatus.recording = true
      if (!callStatus.talking && !callStatus.thinking) {
        callStatus.micOpening = false
      }
    }
  }
)

onMounted(() => {
  audioStream()
  open()
})

onBeforeUnmount(() => {
  close()
  myRecorder.value && myRecorder.value.close()
  myRecorder.value = null

  myStream.value && myStream.value.stop()
  myStream.value = null
})
</script>

<template>
  <div
    class="phone-page"
    :style="
      agentMessage.bg_info?.bg_url && {
        background: `url('${agentMessage.bg_info?.bg_url}') center / cover no-repeat`
      }
    "
  >
    <div class="phone-background">
      <div class="top-bar">
        <div class="bar">
          <div
            class="left-arrow"
            @click="hangOut"
          >
            <SvgIcon icon-class="left-arrow" />
          </div>
          <div
            class="fsize-18"
            @click="countdownVisible = true"
          >
            {{ agentMessage.name }}
          </div>
        </div>
      </div>
      <div
        class="down-content"
        :style="callStatus.inputting && 'margin-top: -50%'"
      >
        <div class="sound-tips mb-16">{{ t('clearAudioEnvironment') }}</div>
        <div class="loading-phone-call mb-26">
          <div class="left-line"></div>
          <div class="dots">
            <div
              v-for="i in 7"
              :key="i"
              :class="handleDot()"
            ></div>
          </div>
          <div class="right-line"></div>
        </div>
        <div
          v-if="callStatus.connecting"
          class="mb-12"
        >
          连接中...
        </div>
        <div
          v-else
          class="text-container mb-12"
          ref="agentTextRef"
        >
          <div class="placeholder"></div>
          {{ agentText }}
          <div class="placeholder"></div>
        </div>
        <div
          class="time-container mb-32"
          v-show="!callStatus.inputting"
        >
          <div
            class="charge"
            @click="handleTopUp"
          >
            充值获取<span class="purple-link">更多时长</span>
          </div>
        </div>
        <div
          class="btn-container mb-8"
          v-show="!callStatus.inputting"
        >
          <div class="flex-center-center flex-column rg-12">
            <div
              class="hang-out"
              @click="hangOut"
            >
              <SvgIcon icon-class="hang-out" />
            </div>
            <div class="btn-text">{{ t('hangUp') }}</div>
          </div>
          <div class="flex-center-center flex-column rg-12">
            <div
              class="mic"
              :class="hadSound && volumeNum && 'mic-volume'"
              :style="{
                '--border-width': `${volumeNum * 0.06}px`,
                opacity: !callStatus.recording || callStatus.thinking || callStatus.talking ? 0.5 : 1
              }"
              @click="debouncePauseRestart"
            >
              <SvgIcon :icon-class="callStatus.micOpening ? 'microphone-call' : 'microphone-close'" />
            </div>
            <div class="btn-text">{{ callStatus.micOpening ? t('microphoneOn') : '麦克风关闭' }}</div>
          </div>
        </div>
        <div
          class="keyboard"
          v-show="!callStatus.inputting"
          @click="openKeyboard"
        >
          <div class="w-full flex-center-center cg-4"><SvgIcon icon-class="keyboard-white" />{{ t('showKeyboard') }}</div>
        </div>
        <div
          class="input-container"
          v-show="callStatus.inputting"
        >
          <van-field
            ref="inputRef"
            type="textarea"
            class="flex-1 input"
            v-model="question"
            :autosize="{
              maxHeight: 100
            }"
            rows="1"
          />
          <SvgIcon
            v-if="question"
            icon-class="send"
            class="fsize-28"
            @click="sendText"
          />
          <SvgIcon
            v-else
            icon-class="microphone"
            class="fsize-28"
            @click="callStatus.inputting = false"
          />
        </div>
      </div>
    </div>
    <CountdownHangoutPopup
      v-model="countdownVisible"
      @hang-out="hangOut"
      @reset="resetCountDown"
    />
    <CallTimeTopUpPopup
      v-model="topUpVisible"
      @reset="resetCountDown"
      @hang-out="hangOut"
      :should-hangout="shouldHangout"
    />
  </div>
</template>

<style scoped lang="scss">
.phone-page {
  position: absolute;
  z-index: 6;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overscroll-behavior: none;
  background: url('/ugenie.png') center / 50% no-repeat;
}

.phone-background {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, rgba(24, 24, 24, 50%) 0%, rgba(24, 24, 24, 0%) 30%, rgba(24, 24, 24, 0%) 60%, #181818 100%);
}

.down-content {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  width: 100%;
  padding: 0 32px;

  .sound-tips {
    padding: 4px 12px;
    font-size: 11px;
    font-weight: 400;
    line-height: 13px;
    color: rgba(255, 255, 255, 50%);
    background: rgba(21, 21, 21, 60%);
    border-radius: 28px;
  }

  .loading-phone-call {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 16px;

    .left-line {
      flex: 1;
      height: 1px;
      background: linear-gradient(90deg, rgba(255, 255, 255, 0%) 0%, #fff 100%);
      border-radius: 7px;
    }

    .dots {
      display: flex;
      column-gap: 4px;
      align-items: center;
      margin: 0 8px;

      .dot-circle {
        width: 6px;
        height: 6px;
        background: #fff;
        border-radius: 50%;
        opacity: 0;
        animation: fadeInOut 1s linear infinite;

        // 通过循环自动生成 nth-child 延迟
        @for $i from 1 through 7 {
          &:nth-child(#{$i}) {
            animation-delay: 0.1s * ($i - 1);
          }
        }
      }

      .dot-sound {
        width: 4px;
        height: 6px;
        background: #fff;
        border-radius: 22px;
        animation: dotAnimation 600ms linear infinite;

        // 使用循环为每个 dot 元素添加不同的延迟时间
        @for $i from 1 through 7 {
          &:nth-child(#{$i}) {
            animation-delay: 0.1s * ($i - 1); // 动态计算延迟
          }
        }
      }

      .dot {
        width: 4px;
        height: 6px;
        background: #fff;
        border-radius: 22px;
      }

      /* HTML: <div class="loader"></div> */
      .loader {
        width: 60px;
        aspect-ratio: 4;
        clip-path: inset(0 100% 0 0);
        background: radial-gradient(circle closest-side, #000 90%, #0000) 0 / calc(100% / 3) 100% space;
        animation: l1 1s steps(4) infinite;
      }

      @keyframes l1 {
        to {
          clip-path: inset(0 -34% 0 0);
        }
      }
    }

    .right-line {
      flex: 1;
      height: 1px;
      background: linear-gradient(90deg, #fff 0%, rgba(255, 255, 255, 0%) 100%);
      border-radius: 7px;
    }
  }

  .text-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 66px;
    overflow-y: auto;
    font-size: 14px;
    line-height: 22px;
    word-break: break-all;
    mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 0%) 0%, rgba(0, 0, 0, 100%) 22px, rgba(0, 0, 0, 100%) 44px, rgba(0, 0, 0, 0%) 100%);

    .placeholder {
      flex-shrink: 0;
      width: 100%;
      height: 22px;
    }
  }

  .time-container {
    display: flex;
    flex-direction: column;
    row-gap: 4px;
    align-items: center;
    justify-content: center;
    padding: 8px 24px;
    background: rgba(24, 24, 24, 60%);
    backdrop-filter: blur(16px);
    border-radius: 28px;

    .time {
      font-weight: 500;
    }

    .charge {
      font-size: 11px;
      font-weight: 400;

      .purple-link {
        color: $livCoThemeColor;
        text-decoration: underline;
      }
    }
  }

  .btn-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0 42px;
    font-size: 32px;

    .hang-out {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 62px;
      height: 62px;
      background: #ff5b4d;
      border: 1px solid rgba(255, 255, 255, 40%);
      border-radius: 24px;
    }

    .mic {
      box-sizing: content-box;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 62px;
      height: 62px;
      background: rgba(133, 120, 137, 50%);
      backdrop-filter: blur(68px);
      border: 1px solid rgba(223, 219, 255, 20%);
      border-radius: 24px;
    }

    .mic-volume {
      outline: var(--border-width) solid $livCoThemeColor;
    }

    .btn-text {
      font-size: 12px;
      font-weight: 400;
      color: rgba(255, 255, 255, 50%);
    }
  }

  .keyboard {
    display: flex;
    justify-content: center;
    width: 100%;
    padding-bottom: 12px;
    margin-top: auto;
    font-size: 12px;
    font-weight: 400;
    color: #b7b7b7;
  }

  .input-container {
    position: fixed;
    bottom: 0;
    display: flex;
    column-gap: 12px;
    align-items: center;
    width: 100%;
    padding: 6px 12px;
  }

  .input {
    background: rgba(133, 120, 137, 50%);
    border: 1px solid rgba(223, 219, 255, 20%);
    border-radius: 16px;

    :deep(.van-field) {
      height: 48px;
    }
  }
}

.top-bar {
  flex: 1;
  width: 100%;

  .bar {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 44px;

    .left-arrow {
      position: absolute;
      left: 12px;
      font-size: 24px;
    }
  }
}

// 定义关键帧动画
@keyframes fadeInOut {
  0% {
    opacity: 0;
  }

  10% {
    opacity: 1;
  }

  30% {
    opacity: 0;
  }

  100% {
    opacity: 0;
  }
}
// 定义关键帧动画，模拟高度变化的效果
@keyframes dotAnimation {
  0% {
    height: 6px;
  }

  50% {
    height: 16px; // 中间时高度增加到 16px
  }

  100% {
    height: 6px; // 最后恢复到 6px
  }
}
</style>
