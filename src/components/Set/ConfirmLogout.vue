<script setup lang="ts">
import { logout } from '@/api/login'
import { removeToken, removeTourist, removeLoginId } from '@/utils/auth.ts'
import useUserStore from '@/stores/modules/user'
import useNotifyStore from '@/stores/modules/notification.ts'
import useHomeStore from '@/stores/modules/home'
const userStore = useUserStore()
const homeStore = useHomeStore()
const notifyStore = useNotifyStore()
const router = useRouter()
const { t } = useI18n()
const emits = defineEmits<{
  close: []
}>()
const show = ref(true)
function close() {
  emits('close')
}
function submit() {
  logout().then(() => {
    removeToken()
    removeLoginId()
    removeTourist()
    userStore.clearUserInfo()
    userStore.updateUserToken()
    // 清除本地文本消息弹窗记录，下次登录时重新弹出
    close()
    notifyStore.mqttInstance?.disconnect()
    homeStore.setUpdateBanner(true)
    router.push({ name: 'Home' })
  })
}
</script>
<template>
  <van-dialog
    v-model:show="show"
    show-cancel-button
    class="logoutDialog"
  >
    <div class="flex-center-center flex-column content">
      <div class="title">{{ t('logoutOption') }}？</div>
      <div class="btnbox flex-between-center">
        <div
          class="btn flex-center-center btn1"
          @click="close"
        >
          {{ t('cancelButton') }}
        </div>
        <div
          class="btn flex-center-center btn2"
          @click="submit"
        >
          {{ t('confirmButton') }}
        </div>
      </div>
    </div>
  </van-dialog>
</template>

<style lang="scss">
.logoutDialog {
  padding: 24px 16px;

  .van-dialog__footer {
    display: none !important;
  }

  .content {
    gap: 18px;

    .title {
      padding: 24px 24px 26px;
      font-size: 18px;
      font-weight: 500;
      color: #fff;
    }

    .tip {
      width: 249px;
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
      color: #c1c1c1;
      text-align: center;
    }

    .btnbox {
      width: 100%;

      .btn {
        width: 134px;
        height: 48px;
        border-radius: 12px;
      }

      .btn1 {
        color: #c1c1c1;
        background: #3d3c3c;
      }

      .btn2 {
        color: $livCoTextColor;
        background: $livCoThemeColor;
      }
    }
  }
}
</style>
