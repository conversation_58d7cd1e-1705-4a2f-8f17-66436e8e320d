<script lang="ts" setup>
import useAppStore from '@/stores/modules/app'
import flutter from '@/hooks/useFlutter.ts'
import useVersionStore from '@/stores/modules/version'

const versionStore = useVersionStore()
const { t } = useI18n()
const appStore = useAppStore()
const show = ref(true)

function updateNowHandle() {
  if (appStore.isFlutter) {
    flutter.jumpStore()
  }
}

function updateLaterHandle() {
  if (versionStore.isShowEntryApp) {
    localStorage.setItem('laterUpdateCode', `${versionStore.versionUpdateInfo.version_code}`)
    versionStore.isShowEntryApp = false
  }
  appStore.showVersionPopup = false
}
</script>

<template>
  <van-popup
    v-model:show="show"
    show-cancel-button
    class="versionDialog"
    :close-on-click-overlay="false"
  >
    <div class="bg-image"></div>
    <div class="rocket"></div>
    <div class="title">{{ t('discoverUpdate') }}</div>
    <div class="version-number mb-12 mt-8">V{{ versionStore.versionUpdateInfo.version_name }}</div>
    <div
      class="content mb-16"
      v-html="versionStore.versionUpdateInfo.update_content"
    ></div>
    <div
      class="btn flex-center-center"
      @click="updateNowHandle"
    >
      {{ t('updateNow') }}
    </div>
    <div
      class="text mt-16"
      @click="updateLaterHandle"
      v-if="versionStore.versionUpdateInfo.update_type !== 3"
    >
      {{ t('updateLater') }}
    </div>
  </van-popup>
</template>

<style lang="scss" scoped>
.versionDialog {
  width: 307px;
  padding: 24px 16px;
  overflow-y: visible !important;
  // height: 426px;
  background: #383638;
  border: 2px solid #4c484c;
  border-radius: 24px;

  .bg-image {
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100%;
    height: 129px;
    background: url('~@/assets/images/common/version-update.png');
    background-size: contain;
  }

  .rocket {
    position: absolute;
    top: -20px;
    right: 0;
    z-index: -1;
    width: 148px;
    height: 180px;
    background: url('~@/assets/images/common/rocket.png');
    background-size: contain;
  }

  .title {
    width: 159px;
    font-size: 20px;
    font-weight: 600;
    line-height: 22px;
    color: #fff;
  }

  .version-number {
    font-size: 14px;
    font-weight: 500;
    line-height: 14px;
    color: rgba(255, 255, 255, 50%);
  }

  .content {
    width: 100%;
    height: 208px;
    padding: 12px;
    overflow-y: scroll;
    font-size: 12px;
    line-height: 16px;
    color: #fff;
    background: #454245;
    border-radius: 12px;
  }

  .btn {
    width: 100%;
    height: 48px;
    font-size: 16px;
    font-weight: 600;
    line-height: 16px;
    color: #fff;
    background: #ff35f2;
    border-radius: 56px;
    box-shadow:
      inset 0 0 32px 0 #ffcefc,
      inset 0 0 10px 0 rgba(255, 255, 255, 25%);
  }

  .text {
    font-size: 12px;
    line-height: 12px;
    color: rgba(255, 255, 255, 50%);
    text-align: center;
  }
}
</style>
