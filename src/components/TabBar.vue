<script setup lang="ts">
// import useAgentCreate from '@/stores/modules/agentCreate'
// import useAppStore from '@/stores/modules/app'
import useUserStore from '@/stores/modules/user'
import { eventReport, EventTypeEnum, ForwardAddressEnum } from '@/api/eventReport'

const userStore = useUserStore()
// const appStore = useAppStore()
// const agentCreateStore = useAgentCreate()

// const { t } = useI18n()
const active = ref(0)
const route = useRoute()
const reportTypeList = [EventTypeEnum.OPEN_HOME, EventTypeEnum.OPEN_MESSAGE_CHAT_HISTORY, EventTypeEnum.OPEN_LEADERBOARD, EventTypeEnum.OPEN_MINE]
const forwardAddressList = [ForwardAddressEnum.HOME_PAGE, ForwardAddressEnum.MESSAGE_CHAT_HISTORY, ForwardAddressEnum.RANKING_PAGE, ForwardAddressEnum.MINE]
const display = computed(() => {
  return route.meta.level && route.meta.level !== 2
})
const beforeChange = (name: number | string) => {
  // if (name === 'agentCreate') {
  //   if (!userStore.token) {
  //     appStore.showLogin = true
  //     return false
  //   } else {
  //     agentCreateStore.showAgentCreateModePopup = true
  //     return false
  //   }
  // }
  if (userStore.token) {
    eventReport({
      event_type: reportTypeList[name as number]
    }).catch((err) => {
      console.warn(err)
    })
  }
  sessionStorage.setItem('login_front_address', forwardAddressList[name as number])

  return true
}
</script>

<template>
  <van-tabbar
    class="tabbar"
    v-show="display"
    v-model="active"
    route
    :before-change="beforeChange"
  >
    <van-tabbar-item
      replace
      to="/"
    >
      <template #icon="props">
        <SvgIcon
          :icon-class="props.active ? 'home-tab-active' : 'home-tab'"
          class="fsize-24"
        />
      </template>
    </van-tabbar-item>
    <van-tabbar-item
      replace
      :to="{ name: 'agentMessage' }"
    >
      <template #icon="props">
        <SvgIcon
          :icon-class="props.active ? 'message-tab-active' : 'message-tab'"
          class="fsize-24"
        />
      </template>
    </van-tabbar-item>
    <van-tabbar-item
      replace
      :to="{ name: 'RankingList' }"
    >
      <template #icon="props">
        <SvgIcon
          :icon-class="props.active ? 'ranking-tab-active' : 'ranking-tab'"
          class="fsize-24"
        />
      </template>
    </van-tabbar-item>
    <van-tabbar-item
      replace
      :to="{ name: 'mine' }"
    >
      <template #icon="props">
        <SvgIcon
          :icon-class="props.active ? 'mine-tab-active' : 'mine-tab'"
          class="fsize-24"
        />
      </template>
    </van-tabbar-item>
  </van-tabbar>
</template>
<style lang="scss" scoped>
@import 'src/assets/styles/variables';

.tabbar {
  height: 62px;
  background: $backColorOpacity;
  backdrop-filter: blur(48px);

  :deep(.van-tabbar-item--active) {
    background: unset;
  }

  :deep(.van-tabbar-item__icon) {
    margin-bottom: -4px;
  }
}
</style>
