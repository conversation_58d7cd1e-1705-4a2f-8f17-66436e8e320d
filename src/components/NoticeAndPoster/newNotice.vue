<template>
  <div
    v-if="visible"
    id="notice-message"
    class="newNotice-box"
    @touchmove="onTouchmove"
    @touchstart="onTouchstart"
    @touchend="onTouchend"
  >
    <div class="newNotice">
      <div class="title">
        <div
          v-if="isReward"
          class="reward"
        >
          <!--          水晶/钻石-->
          <img
            v-if="currentNotice.type === 1"
            src="@/assets/images/mine/mine-crystal.png"
            alt="diamond"
          />
          <!--          金币-->
          <img
            v-else
            src="@/assets/images/mine/gold-icon.png"
            alt="gold"
          />
        </div>
        <van-icon
          v-else-if="currentNotice.title"
          name="warning-o"
          size="14px"
          color="#FFFFFF"
        />
        <!-- 系统消息（普通 0、补偿 5） -->
        <span v-if="[0, 5].includes(Number(currentNotice.type)) || currentNotice.title">
          {{ currentNotice.title }}
        </span>
        <span v-else-if="[1, 2].includes(Number(currentNotice.type))"> {{ currentNotice.amount + (currentNotice.type === 1 ? '钻石' : '体力') }}到账 </span>
        <!-- 关注了智能体或个人，点赞智能体即关注 -->
        <span v-else-if="[3, 4].includes(currentNotice.type)">
          {{
            currentNotice.ai_id === 0
              ? replaceStars($t('followedYouNotice'))
              : replaceStars($t('followedYour'), [currentNotice.promoter_client_info?.name, currentNotice.ai_info?.name])
          }}
        </span>
      </div>
      <div
        v-if="currentNotice.content"
        class="detail-box"
        :style="{
          marginTop: currentNotice.title ? '5px' : 0
        }"
      >
        <text>{{ currentNotice.content }}</text>
      </div>
    </div>
    <van-icon
      v-if="currentNotice.redirect_url"
      name="arrow"
      size="22"
      color="#eee"
      @click="checkDetail"
    />
  </div>
</template>
<script setup lang="ts">
import type { UnReadMsgItem } from '@/api/msg/type.ts'
import anime from 'animejs'
import { isEmpty } from 'lodash-es'
import { replaceStars } from '@/utils'

const emits = defineEmits(['close', 'clickNotice'])
const props = withDefaults(
  defineProps<{
    currentNotice: UnReadMsgItem
  }>(),
  {
    currentNotice: () => ({
      type: 0,
      title: '',
      content: '',
      redirect_url: '',
      amount: 0,
      ai_info: {
        name: '',
        image_url: ''
      },
      ai_id: 0,
      is_read: 0,
      id: 0,
      promoter_client_info: {
        avatar_url: '',
        name: ''
      },
      promoter_client_id: 0
    })
  }
)
const visible = defineModel<boolean>()

const router = useRouter()
const isOpen = ref(false)
const touchParams = reactive<{ moveClientY: number; startClientY: number }>({
  moveClientY: 0,
  startClientY: 0
})
const timer = ref()

const isReward = computed(() => [1, 2].includes(props.currentNotice.type))

const checkDetail = () => {
  // const reg1 = /^\/inwebdiv\//
  // const reg2 = /^(https?:\/\/)/i
  // if (reg1.test(props.currentNotice.redirect_url)) {
  //   uni.navigateTo({ url: '/pages/public/webdiv?url=' + props.currentNotice.redirect_url.replace('/inwebdiv/', '') })
  // } else if (reg2.test(props.currentNotice.redirect_url)) {
  if (props.currentNotice.redirect_url === '/MessageDetail') {
    router.push(`/Mine/MessageDetail?msg_id=${props.currentNotice?.id}`)
  } else {
    window.open(props.currentNotice.redirect_url)
  }
  closeAnimation()
  // }
  emits('clickNotice')
}

const onTouchmove = (e: TouchEvent) => {
  touchParams.moveClientY = e.changedTouches[0].clientY
}
const onTouchstart = (e: TouchEvent) => {
  touchParams.startClientY = e.changedTouches[0].clientY
}
const onTouchend = () => {
  if (touchParams.startClientY - touchParams.moveClientY >= 20 && touchParams.moveClientY !== 0) {
    touchParams.startClientY = 0
    touchParams.moveClientY = 0
    timer.value && clearTimeout(timer.value)
    closeAnimation()
  }
}
const closeAnimation = () => {
  let box = document.querySelector('#notice-message')
  const animation = anime({
    targets: box,
    translateY: ['14px', '-100%'],
    translateX: ['-50%', '-50%'],
    top: ['0', '-10%'],
    easing: 'easeInOutQuad'
  })
  animation.finished.then(() => {
    isOpen.value = false
    emits('close')
  })
  box = null
}

const startInterval = () => {
  timer.value = setTimeout(() => {
    clearTimeout(timer.value)
    closeAnimation()
  }, 4 * 1000)
}

const openAnimation = () => {
  if (isOpen.value) {
    return
  }
  isOpen.value = true
  let box = document.querySelector('#notice-message')
  const animation = anime({
    targets: box,
    translateY: ['-100%', '14px'],
    translateX: ['-50%', '-50%'],
    top: ['-10%', '0'],
    easing: 'easeInOutQuad'
  })
  animation.finished.then(() => {
    startInterval()
  })
  box = null
}

watch(
  () => [visible, props.currentNotice],
  ([visible, notice]) => {
    if (visible && !isEmpty(notice)) {
      nextTick(() => {
        openAnimation()
      })
    }
  },
  {
    deep: true,
    immediate: true
  }
)

defineExpose({
  openAnimation,
  closeAnimation
})
</script>

<style lang="scss" scoped>
.newNotice-box {
  position: fixed;
  top: -12%;
  left: 50%;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: calc(100% - 24px);
  padding: 12px;
  font-size: 16px;
  color: #fff;
  background: #181818e5;
  backdrop-filter: blur(80px);
  border-radius: 16px;
  transform: translateX(-50%);

  .newNotice {
    width: 100%;

    .title {
      .reward {
        padding: 5px 5px 0;
        margin-right: 12px;
        background: rgba(255, 255, 255, 30%);
        border-radius: 12px;

        img {
          width: 24px;
        }
      }

      .van-icon {
        margin-top: 2px;
        margin-right: 4px;
      }

      display: flex;
      align-items: center;

      span {
        font-size: 14px;
        font-weight: 600;
        word-break: normal;
        white-space: normal;
      }
    }

    .detail-box {
      font-size: 12px;
      line-height: 1.5;
      color: rgba(255, 255, 255, 80%);
      word-break: normal;

      .detail {
        margin-left: 5px;
        color: #2196f3;
      }
    }
  }
}
</style>
