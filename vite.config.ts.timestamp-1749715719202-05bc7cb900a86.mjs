// vite.config.ts
import path2 from "node:path";
import { loadEnv } from "file:///D:/project/eros-wap/node_modules/.pnpm/vite@5.2.0_@types+node@20.12.7_sass@1.77.1/node_modules/vite/dist/node/index.js";
import viewport from "file:///D:/project/eros-wap/node_modules/.pnpm/postcss-mobile-forever@4.1.5_postcss@8.4.38/node_modules/postcss-mobile-forever/index.js";
import autoprefixer from "file:///D:/project/eros-wap/node_modules/.pnpm/autoprefixer@10.4.19_postcss@8.4.38/node_modules/autoprefixer/lib/autoprefixer.js";

// build/vite/index.ts
import { dirname, resolve } from "node:path";
import { fileURLToPath } from "node:url";
import vue from "file:///D:/project/eros-wap/node_modules/.pnpm/@vitejs+plugin-vue@5.0.4_vi_88553dbbadce69775f39a95717c5743e/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import AutoImport from "file:///D:/project/eros-wap/node_modules/.pnpm/unplugin-auto-import@0.17.5_ec5dc6380e78d68ecb858f71d9ed27fc/node_modules/unplugin-auto-import/dist/vite.js";
import { PrimeVueResolver, VantResolver } from "file:///D:/project/eros-wap/node_modules/.pnpm/unplugin-vue-components@0.2_7f96ea38fd7d5c6854c08d4c72392ae0/node_modules/unplugin-vue-components/dist/resolvers.js";
import Components from "file:///D:/project/eros-wap/node_modules/.pnpm/unplugin-vue-components@0.2_7f96ea38fd7d5c6854c08d4c72392ae0/node_modules/unplugin-vue-components/dist/vite.js";
import { VueRouterAutoImports } from "file:///D:/project/eros-wap/node_modules/.pnpm/unplugin-vue-router@0.8.6_r_e0964595d4818ce0691d7ac23b2ced51/node_modules/unplugin-vue-router/dist/index.mjs";
import VueRouter from "file:///D:/project/eros-wap/node_modules/.pnpm/unplugin-vue-router@0.8.6_r_e0964595d4818ce0691d7ac23b2ced51/node_modules/unplugin-vue-router/dist/vite.mjs";
import mockDevServerPlugin from "file:///D:/project/eros-wap/node_modules/.pnpm/vite-plugin-mock-dev-server_edcf12828a6a6fdaf1fe1ad35b8ec9d1/node_modules/vite-plugin-mock-dev-server/dist/index.js";
import { visualizer } from "file:///D:/project/eros-wap/node_modules/.pnpm/rollup-plugin-visualizer@5.12.0_rollup@4.21.3/node_modules/rollup-plugin-visualizer/dist/plugin/index.js";
import VueDevTools from "file:///D:/project/eros-wap/node_modules/.pnpm/vite-plugin-vue-devtools@7._011fd7f45fc5e581ff84315fd5b7d16c/node_modules/vite-plugin-vue-devtools/dist/vite.mjs";
import VueI18nPlugin from "file:///D:/project/eros-wap/node_modules/.pnpm/@intlify+unplugin-vue-i18n@_ac25135747b9e215a800603e1a668ab2/node_modules/@intlify/unplugin-vue-i18n/lib/vite.mjs";

// build/vite/vconsole.ts
import path from "node:path";
import { viteVConsole } from "file:///D:/project/eros-wap/node_modules/.pnpm/vite-plugin-vconsole@2.1.1/node_modules/vite-plugin-vconsole/dist/main.mjs";
function createViteVConsole(viteEnv) {
  const { VITE_APP_ENV } = viteEnv;
  return viteVConsole({
    entry: [path.resolve("src/main.ts")],
    enabled: VITE_APP_ENV !== "production",
    config: {
      maxLogNumber: 1e3,
      theme: "light"
    },
    // https://github.com/vadxq/vite-plugin-vconsole/issues/21
    dynamicConfig: {
      theme: `document.documentElement.classList.contains('dark') ? 'dark' : 'light'`
    },
    eventListener: `
      const targetElement = document.querySelector('html'); // \u62E9\u8981\u76D1\u542C\u7684\u5143\u7D20
      const observerOptions = {
        attributes: true, // \u76D1\u542C\u5C5E\u6027\u53D8\u5316
        attributeFilter: ['class'] // \u53EA\u76D1\u542Cclass\u5C5E\u6027\u53D8\u5316
      };

      // \u5B9A\u4E49\u56DE\u8C03\u51FD\u6570\u6765\u5904\u7406\u89C2\u5BDF\u5230\u7684\u53D8\u5316
      function handleAttributeChange(mutationsList) {
        for(let mutation of mutationsList) {
          if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
            if (window && window.vConsole) {
              window.vConsole.dynamicChange.value = new Date().getTime();
            }
          }
        }
      }

      // \u521B\u5EFA\u89C2\u5BDF\u8005\u5B9E\u4F8B\u5E76\u4F20\u5165\u56DE\u8C03\u51FD\u6570
      const observer = new MutationObserver(handleAttributeChange);

      // \u5F00\u59CB\u89C2\u5BDF\u76EE\u6807\u5143\u7D20
      observer.observe(targetElement, observerOptions);

      // \u5F53\u4E0D\u518D\u9700\u8981\u89C2\u5BDF\u65F6\uFF0C\u505C\u6B62\u89C2\u5BDF
      // observer.disconnect();
    `
  });
}

// build/vite/index.ts
var __vite_injected_original_import_meta_url = "file:///D:/project/eros-wap/build/vite/index.ts";
function createVitePlugins(viteEnv, isBuild = false) {
  return [
    // createSvgIcon(isBuild),
    // https://github.com/posva/unplugin-vue-router
    VueRouter({
      extensions: [".vue"],
      routesFolder: "src/pages",
      dts: "src/typed-router.d.ts"
    }),
    vue({
      template: {
        compilerOptions: {
          isCustomElement: (tag) => tag.startsWith("swiper-")
        }
      }
    }),
    // 开启https测试
    // basicSsl(),
    // https://github.com/jbaubree/vite-plugin-sitemap
    // Sitemap(),
    visualizer(),
    // https://github.com/pengzhanbo/vite-plugin-mock-dev-server
    mockDevServerPlugin(),
    // https://github.com/antfu/unplugin-vue-components
    Components({
      extensions: ["vue"],
      resolvers: [VantResolver(), PrimeVueResolver()],
      include: [/\.vue$/, /\.vue\?vue/],
      dts: "src/components.d.ts"
    }),
    // https://github.com/antfu/unplugin-auto-import
    AutoImport({
      include: [/\.[tj]sx?$/, /\.vue$/, /\.vue\?vue/, /\.ts$/],
      imports: [
        "vue",
        "vitest",
        "@vueuse/core",
        "pinia",
        VueRouterAutoImports,
        {
          "vue-router/auto": ["useLink"],
          "@/utils/i18n": ["i18n", "locale"],
          "vue-i18n": ["useI18n"]
        }
        // unheadVueComposablesImports,
      ],
      dts: "src/auto-imports.d.ts"
    }),
    // https://github.com/intlify/bundle-tools/tree/main/packages/unplugin-vue-i18n
    VueI18nPlugin({
      // locale messages resource pre-compile option
      include: resolve(dirname(fileURLToPath(__vite_injected_original_import_meta_url)), "../../src/locales/**")
    }),
    // legacy({
    //   targets: ['defaults', 'not IE 11'],
    // }),
    // https://github.com/antfu/unocss
    // see uno.config.ts for config
    // UnoCSS(),
    // https://github.com/vadxq/vite-plugin-vconsole
    createViteVConsole(viteEnv),
    // https://github.com/vuejs/devtools-next
    VueDevTools()
    // https://github.com/antfu/vite-plugin-pwa
    // VitePWA({
    //   registerType: 'autoUpdate',
    //   includeAssets: ['favicon.svg', 'safari-pinned-tab.svg'],
    //   manifest: {
    //     name: 'vue3-vant-mobile',
    //     short_name: 'vue3-vant-mobile',
    //     theme_color: '#ffffff',
    //     icons: [
    //       {
    //         src: '/pwa-192x192.png',
    //         sizes: '192x192',
    //         type: 'image/png',
    //       },
    //       {
    //         src: '/pwa-512x512.png',
    //         sizes: '512x512',
    //         type: 'image/png',
    //       },
    //       {
    //         src: '/pwa-512x512.png',
    //         sizes: '512x512',
    //         type: 'image/png',
    //         purpose: 'any maskable',
    //       },
    //     ],
    //   },
    // }),
  ];
}

// vite.config.ts
var __vite_injected_original_dirname = "D:\\project\\eros-wap";
var vite_config_default = ({ mode, command }) => {
  const root = process.cwd();
  const env = loadEnv(mode, root);
  const { VITE_APP_ENV, VITE_APP_PUBLIC_PATH } = env;
  return {
    base: VITE_APP_PUBLIC_PATH,
    plugins: createVitePlugins(env, command === "build"),
    esbuild: {
      pure: VITE_APP_ENV === "test" ? [] : ["console.log"]
    },
    server: {
      host: true,
      port: 8080,
      proxy: {
        "/api": {
          // target: 'https://livco.me',
          target: "https://livco-dev.wujialin.top/",
          ws: false,
          changeOrigin: true
          // rewrite: (path) => path.replace(/^\/api/, '')
        }
      }
    },
    resolve: {
      alias: {
        "~@": path2.join(__vite_injected_original_dirname, "./src"),
        "@": path2.join(__vite_injected_original_dirname, "./src"),
        "~": path2.join(__vite_injected_original_dirname, "./src/assets")
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@import 'src/assets/styles/variables';`
        }
      },
      postcss: {
        plugins: [
          autoprefixer(),
          // https://github.com/wswmsword/postcss-mobile-forever
          viewport({
            appSelector: "#app",
            viewportWidth: 375,
            maxDisplayWidth: 500,
            propertyBlackList: ["border", "border-radius", "clip-path"],
            selectorBlackList: ["loading-phone-call", "guide-border", "loading-bar"],
            rootContainingBlockSelectorList: ["van-tabbar", "van-popup", "van-popover", "top-bar"]
          })
        ]
      }
    }
  };
};
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsidml0ZS5jb25maWcudHMiLCAiYnVpbGQvdml0ZS9pbmRleC50cyIsICJidWlsZC92aXRlL3Zjb25zb2xlLnRzIl0sCiAgInNvdXJjZXNDb250ZW50IjogWyJjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfZGlybmFtZSA9IFwiRDpcXFxccHJvamVjdFxcXFxlcm9zLXdhcFwiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9maWxlbmFtZSA9IFwiRDpcXFxccHJvamVjdFxcXFxlcm9zLXdhcFxcXFx2aXRlLmNvbmZpZy50c1wiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9pbXBvcnRfbWV0YV91cmwgPSBcImZpbGU6Ly8vRDovcHJvamVjdC9lcm9zLXdhcC92aXRlLmNvbmZpZy50c1wiO2ltcG9ydCBwYXRoIGZyb20gJ25vZGU6cGF0aCdcclxuaW1wb3J0IHsgbG9hZEVudiB9IGZyb20gJ3ZpdGUnXHJcbmltcG9ydCB0eXBlIHsgQ29uZmlnRW52LCBVc2VyQ29uZmlnIH0gZnJvbSAndml0ZSdcclxuaW1wb3J0IHZpZXdwb3J0IGZyb20gJ3Bvc3Rjc3MtbW9iaWxlLWZvcmV2ZXInXHJcbmltcG9ydCBhdXRvcHJlZml4ZXIgZnJvbSAnYXV0b3ByZWZpeGVyJ1xyXG5pbXBvcnQgeyBjcmVhdGVWaXRlUGx1Z2lucyB9IGZyb20gJy4vYnVpbGQvdml0ZSdcclxuXHJcbi8vIGh0dHBzOi8vdml0ZWpzLmRldi9jb25maWcvXHJcbmV4cG9ydCBkZWZhdWx0ICh7IG1vZGUsIGNvbW1hbmQgfTogQ29uZmlnRW52KTogVXNlckNvbmZpZyA9PiB7XHJcbiAgY29uc3Qgcm9vdCA9IHByb2Nlc3MuY3dkKClcclxuICBjb25zdCBlbnYgPSBsb2FkRW52KG1vZGUsIHJvb3QpXHJcbiAgY29uc3QgeyBWSVRFX0FQUF9FTlYsIFZJVEVfQVBQX1BVQkxJQ19QQVRIIH0gPSBlbnZcclxuXHJcbiAgcmV0dXJuIHtcclxuICAgIGJhc2U6IFZJVEVfQVBQX1BVQkxJQ19QQVRILFxyXG4gICAgcGx1Z2luczogY3JlYXRlVml0ZVBsdWdpbnMoZW52LCBjb21tYW5kID09PSAnYnVpbGQnKSxcclxuICAgIGVzYnVpbGQ6IHtcclxuICAgICAgcHVyZTogVklURV9BUFBfRU5WID09PSAndGVzdCcgPyBbXSA6IFsnY29uc29sZS5sb2cnXVxyXG4gICAgfSxcclxuICAgIHNlcnZlcjoge1xyXG4gICAgICBob3N0OiB0cnVlLFxyXG4gICAgICBwb3J0OiA4MDgwLFxyXG4gICAgICBwcm94eToge1xyXG4gICAgICAgICcvYXBpJzoge1xyXG4gICAgICAgICAgLy8gdGFyZ2V0OiAnaHR0cHM6Ly9saXZjby5tZScsXHJcbiAgICAgICAgICB0YXJnZXQ6ICdodHRwczovL2xpdmNvLWRldi53dWppYWxpbi50b3AvJyxcclxuICAgICAgICAgIHdzOiBmYWxzZSxcclxuICAgICAgICAgIGNoYW5nZU9yaWdpbjogdHJ1ZVxyXG4gICAgICAgICAgLy8gcmV3cml0ZTogKHBhdGgpID0+IHBhdGgucmVwbGFjZSgvXlxcL2FwaS8sICcnKVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfSxcclxuICAgIHJlc29sdmU6IHtcclxuICAgICAgYWxpYXM6IHtcclxuICAgICAgICAnfkAnOiBwYXRoLmpvaW4oX19kaXJuYW1lLCAnLi9zcmMnKSxcclxuICAgICAgICAnQCc6IHBhdGguam9pbihfX2Rpcm5hbWUsICcuL3NyYycpLFxyXG4gICAgICAgICd+JzogcGF0aC5qb2luKF9fZGlybmFtZSwgJy4vc3JjL2Fzc2V0cycpXHJcbiAgICAgIH1cclxuICAgIH0sXHJcbiAgICBjc3M6IHtcclxuICAgICAgcHJlcHJvY2Vzc29yT3B0aW9uczoge1xyXG4gICAgICAgIHNjc3M6IHtcclxuICAgICAgICAgIGFkZGl0aW9uYWxEYXRhOiBgQGltcG9ydCAnc3JjL2Fzc2V0cy9zdHlsZXMvdmFyaWFibGVzJztgXHJcbiAgICAgICAgfVxyXG4gICAgICB9LFxyXG4gICAgICBwb3N0Y3NzOiB7XHJcbiAgICAgICAgcGx1Z2luczogW1xyXG4gICAgICAgICAgYXV0b3ByZWZpeGVyKCksXHJcbiAgICAgICAgICAvLyBodHRwczovL2dpdGh1Yi5jb20vd3N3bXN3b3JkL3Bvc3Rjc3MtbW9iaWxlLWZvcmV2ZXJcclxuICAgICAgICAgIHZpZXdwb3J0KHtcclxuICAgICAgICAgICAgYXBwU2VsZWN0b3I6ICcjYXBwJyxcclxuICAgICAgICAgICAgdmlld3BvcnRXaWR0aDogMzc1LFxyXG4gICAgICAgICAgICBtYXhEaXNwbGF5V2lkdGg6IDUwMCxcclxuICAgICAgICAgICAgcHJvcGVydHlCbGFja0xpc3Q6IFsnYm9yZGVyJywgJ2JvcmRlci1yYWRpdXMnLCAnY2xpcC1wYXRoJ10sXHJcbiAgICAgICAgICAgIHNlbGVjdG9yQmxhY2tMaXN0OiBbJ2xvYWRpbmctcGhvbmUtY2FsbCcsICdndWlkZS1ib3JkZXInLCAnbG9hZGluZy1iYXInXSxcclxuICAgICAgICAgICAgcm9vdENvbnRhaW5pbmdCbG9ja1NlbGVjdG9yTGlzdDogWyd2YW4tdGFiYmFyJywgJ3Zhbi1wb3B1cCcsICd2YW4tcG9wb3ZlcicsICd0b3AtYmFyJ11cclxuICAgICAgICAgIH0pXHJcbiAgICAgICAgXVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcbiIsICJjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfZGlybmFtZSA9IFwiRDpcXFxccHJvamVjdFxcXFxlcm9zLXdhcFxcXFxidWlsZFxcXFx2aXRlXCI7Y29uc3QgX192aXRlX2luamVjdGVkX29yaWdpbmFsX2ZpbGVuYW1lID0gXCJEOlxcXFxwcm9qZWN0XFxcXGVyb3Mtd2FwXFxcXGJ1aWxkXFxcXHZpdGVcXFxcaW5kZXgudHNcIjtjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfaW1wb3J0X21ldGFfdXJsID0gXCJmaWxlOi8vL0Q6L3Byb2plY3QvZXJvcy13YXAvYnVpbGQvdml0ZS9pbmRleC50c1wiO2ltcG9ydCB7IGRpcm5hbWUsIHJlc29sdmUgfSBmcm9tICdub2RlOnBhdGgnXHJcbmltcG9ydCB7IGZpbGVVUkxUb1BhdGggfSBmcm9tICdub2RlOnVybCdcclxuLy8gaW1wb3J0IHsgdW5oZWFkVnVlQ29tcG9zYWJsZXNJbXBvcnRzIH0gZnJvbSAnQHVuaGVhZC92dWUnXHJcbi8vIGltcG9ydCBsZWdhY3kgZnJvbSAnQHZpdGVqcy9wbHVnaW4tbGVnYWN5J1xyXG5pbXBvcnQgdnVlIGZyb20gJ0B2aXRlanMvcGx1Z2luLXZ1ZSdcclxuXHJcbi8vIGltcG9ydCBiYXNpY1NzbCBmcm9tICdAdml0ZWpzL3BsdWdpbi1iYXNpYy1zc2wnXHJcbi8vIGltcG9ydCBVbm9DU1MgZnJvbSAndW5vY3NzL3ZpdGUnXHJcbmltcG9ydCBBdXRvSW1wb3J0IGZyb20gJ3VucGx1Z2luLWF1dG8taW1wb3J0L3ZpdGUnXHJcbmltcG9ydCB7IFByaW1lVnVlUmVzb2x2ZXIsIFZhbnRSZXNvbHZlciB9IGZyb20gJ3VucGx1Z2luLXZ1ZS1jb21wb25lbnRzL3Jlc29sdmVycydcclxuaW1wb3J0IENvbXBvbmVudHMgZnJvbSAndW5wbHVnaW4tdnVlLWNvbXBvbmVudHMvdml0ZSdcclxuaW1wb3J0IHsgVnVlUm91dGVyQXV0b0ltcG9ydHMgfSBmcm9tICd1bnBsdWdpbi12dWUtcm91dGVyJ1xyXG5pbXBvcnQgVnVlUm91dGVyIGZyb20gJ3VucGx1Z2luLXZ1ZS1yb3V0ZXIvdml0ZSdcclxuaW1wb3J0IG1vY2tEZXZTZXJ2ZXJQbHVnaW4gZnJvbSAndml0ZS1wbHVnaW4tbW9jay1kZXYtc2VydmVyJ1xyXG5pbXBvcnQgeyB2aXN1YWxpemVyIH0gZnJvbSAncm9sbHVwLXBsdWdpbi12aXN1YWxpemVyJ1xyXG4vLyBpbXBvcnQgeyBWaXRlUFdBIH0gZnJvbSAndml0ZS1wbHVnaW4tcHdhJ1xyXG4vLyBpbXBvcnQgU2l0ZW1hcCBmcm9tICd2aXRlLXBsdWdpbi1zaXRlbWFwJ1xyXG5pbXBvcnQgVnVlRGV2VG9vbHMgZnJvbSAndml0ZS1wbHVnaW4tdnVlLWRldnRvb2xzJ1xyXG5pbXBvcnQgVnVlSTE4blBsdWdpbiBmcm9tICdAaW50bGlmeS91bnBsdWdpbi12dWUtaTE4bi92aXRlJ1xyXG5pbXBvcnQgeyBjcmVhdGVWaXRlVkNvbnNvbGUgfSBmcm9tICcuL3Zjb25zb2xlJ1xyXG4vLyBpbXBvcnQgY3JlYXRlU3ZnSWNvbiBmcm9tICcuL3N2Zy1pY29uJ1xyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZVZpdGVQbHVnaW5zKHZpdGVFbnY6IGFueSwgaXNCdWlsZCA9IGZhbHNlKSB7XHJcbiAgcmV0dXJuIFtcclxuICAgIC8vIGNyZWF0ZVN2Z0ljb24oaXNCdWlsZCksXHJcbiAgICAvLyBodHRwczovL2dpdGh1Yi5jb20vcG9zdmEvdW5wbHVnaW4tdnVlLXJvdXRlclxyXG4gICAgVnVlUm91dGVyKHtcclxuICAgICAgZXh0ZW5zaW9uczogWycudnVlJ10sXHJcbiAgICAgIHJvdXRlc0ZvbGRlcjogJ3NyYy9wYWdlcycsXHJcbiAgICAgIGR0czogJ3NyYy90eXBlZC1yb3V0ZXIuZC50cydcclxuICAgIH0pLFxyXG5cclxuICAgIHZ1ZSh7XHJcbiAgICAgIHRlbXBsYXRlOiB7XHJcbiAgICAgICAgY29tcGlsZXJPcHRpb25zOiB7XHJcbiAgICAgICAgICBpc0N1c3RvbUVsZW1lbnQ6ICh0YWcpID0+IHRhZy5zdGFydHNXaXRoKCdzd2lwZXItJylcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH0pLFxyXG4gICAgLy8gXHU1RjAwXHU1NDJGaHR0cHNcdTZENEJcdThCRDVcclxuICAgIC8vIGJhc2ljU3NsKCksXHJcbiAgICAvLyBodHRwczovL2dpdGh1Yi5jb20vamJhdWJyZWUvdml0ZS1wbHVnaW4tc2l0ZW1hcFxyXG4gICAgLy8gU2l0ZW1hcCgpLFxyXG4gICAgdmlzdWFsaXplcigpLFxyXG4gICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL3Blbmd6aGFuYm8vdml0ZS1wbHVnaW4tbW9jay1kZXYtc2VydmVyXHJcbiAgICBtb2NrRGV2U2VydmVyUGx1Z2luKCksXHJcblxyXG4gICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL2FudGZ1L3VucGx1Z2luLXZ1ZS1jb21wb25lbnRzXHJcbiAgICBDb21wb25lbnRzKHtcclxuICAgICAgZXh0ZW5zaW9uczogWyd2dWUnXSxcclxuICAgICAgcmVzb2x2ZXJzOiBbVmFudFJlc29sdmVyKCksIFByaW1lVnVlUmVzb2x2ZXIoKV0sXHJcbiAgICAgIGluY2x1ZGU6IFsvXFwudnVlJC8sIC9cXC52dWVcXD92dWUvXSxcclxuICAgICAgZHRzOiAnc3JjL2NvbXBvbmVudHMuZC50cydcclxuICAgIH0pLFxyXG5cclxuICAgIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS9hbnRmdS91bnBsdWdpbi1hdXRvLWltcG9ydFxyXG4gICAgQXV0b0ltcG9ydCh7XHJcbiAgICAgIGluY2x1ZGU6IFsvXFwuW3RqXXN4PyQvLCAvXFwudnVlJC8sIC9cXC52dWVcXD92dWUvLCAvXFwudHMkL10sXHJcbiAgICAgIGltcG9ydHM6IFtcclxuICAgICAgICAndnVlJyxcclxuICAgICAgICAndml0ZXN0JyxcclxuICAgICAgICAnQHZ1ZXVzZS9jb3JlJyxcclxuICAgICAgICAncGluaWEnLFxyXG4gICAgICAgIFZ1ZVJvdXRlckF1dG9JbXBvcnRzLFxyXG4gICAgICAgIHtcclxuICAgICAgICAgICd2dWUtcm91dGVyL2F1dG8nOiBbJ3VzZUxpbmsnXSxcclxuICAgICAgICAgICdAL3V0aWxzL2kxOG4nOiBbJ2kxOG4nLCAnbG9jYWxlJ10sXHJcbiAgICAgICAgICAndnVlLWkxOG4nOiBbJ3VzZUkxOG4nXVxyXG4gICAgICAgIH1cclxuICAgICAgICAvLyB1bmhlYWRWdWVDb21wb3NhYmxlc0ltcG9ydHMsXHJcbiAgICAgIF0sXHJcbiAgICAgIGR0czogJ3NyYy9hdXRvLWltcG9ydHMuZC50cydcclxuICAgIH0pLFxyXG5cclxuICAgIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS9pbnRsaWZ5L2J1bmRsZS10b29scy90cmVlL21haW4vcGFja2FnZXMvdW5wbHVnaW4tdnVlLWkxOG5cclxuICAgIFZ1ZUkxOG5QbHVnaW4oe1xyXG4gICAgICAvLyBsb2NhbGUgbWVzc2FnZXMgcmVzb3VyY2UgcHJlLWNvbXBpbGUgb3B0aW9uXHJcbiAgICAgIGluY2x1ZGU6IHJlc29sdmUoZGlybmFtZShmaWxlVVJMVG9QYXRoKGltcG9ydC5tZXRhLnVybCkpLCAnLi4vLi4vc3JjL2xvY2FsZXMvKionKVxyXG4gICAgfSksXHJcblxyXG4gICAgLy8gbGVnYWN5KHtcclxuICAgIC8vICAgdGFyZ2V0czogWydkZWZhdWx0cycsICdub3QgSUUgMTEnXSxcclxuICAgIC8vIH0pLFxyXG5cclxuICAgIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS9hbnRmdS91bm9jc3NcclxuICAgIC8vIHNlZSB1bm8uY29uZmlnLnRzIGZvciBjb25maWdcclxuICAgIC8vIFVub0NTUygpLFxyXG5cclxuICAgIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS92YWR4cS92aXRlLXBsdWdpbi12Y29uc29sZVxyXG4gICAgY3JlYXRlVml0ZVZDb25zb2xlKHZpdGVFbnYpLFxyXG5cclxuICAgIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS92dWVqcy9kZXZ0b29scy1uZXh0XHJcbiAgICBWdWVEZXZUb29scygpXHJcblxyXG4gICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL2FudGZ1L3ZpdGUtcGx1Z2luLXB3YVxyXG4gICAgLy8gVml0ZVBXQSh7XHJcbiAgICAvLyAgIHJlZ2lzdGVyVHlwZTogJ2F1dG9VcGRhdGUnLFxyXG4gICAgLy8gICBpbmNsdWRlQXNzZXRzOiBbJ2Zhdmljb24uc3ZnJywgJ3NhZmFyaS1waW5uZWQtdGFiLnN2ZyddLFxyXG4gICAgLy8gICBtYW5pZmVzdDoge1xyXG4gICAgLy8gICAgIG5hbWU6ICd2dWUzLXZhbnQtbW9iaWxlJyxcclxuICAgIC8vICAgICBzaG9ydF9uYW1lOiAndnVlMy12YW50LW1vYmlsZScsXHJcbiAgICAvLyAgICAgdGhlbWVfY29sb3I6ICcjZmZmZmZmJyxcclxuICAgIC8vICAgICBpY29uczogW1xyXG4gICAgLy8gICAgICAge1xyXG4gICAgLy8gICAgICAgICBzcmM6ICcvcHdhLTE5MngxOTIucG5nJyxcclxuICAgIC8vICAgICAgICAgc2l6ZXM6ICcxOTJ4MTkyJyxcclxuICAgIC8vICAgICAgICAgdHlwZTogJ2ltYWdlL3BuZycsXHJcbiAgICAvLyAgICAgICB9LFxyXG4gICAgLy8gICAgICAge1xyXG4gICAgLy8gICAgICAgICBzcmM6ICcvcHdhLTUxMng1MTIucG5nJyxcclxuICAgIC8vICAgICAgICAgc2l6ZXM6ICc1MTJ4NTEyJyxcclxuICAgIC8vICAgICAgICAgdHlwZTogJ2ltYWdlL3BuZycsXHJcbiAgICAvLyAgICAgICB9LFxyXG4gICAgLy8gICAgICAge1xyXG4gICAgLy8gICAgICAgICBzcmM6ICcvcHdhLTUxMng1MTIucG5nJyxcclxuICAgIC8vICAgICAgICAgc2l6ZXM6ICc1MTJ4NTEyJyxcclxuICAgIC8vICAgICAgICAgdHlwZTogJ2ltYWdlL3BuZycsXHJcbiAgICAvLyAgICAgICAgIHB1cnBvc2U6ICdhbnkgbWFza2FibGUnLFxyXG4gICAgLy8gICAgICAgfSxcclxuICAgIC8vICAgICBdLFxyXG4gICAgLy8gICB9LFxyXG4gICAgLy8gfSksXHJcbiAgXVxyXG59XHJcbiIsICJjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfZGlybmFtZSA9IFwiRDpcXFxccHJvamVjdFxcXFxlcm9zLXdhcFxcXFxidWlsZFxcXFx2aXRlXCI7Y29uc3QgX192aXRlX2luamVjdGVkX29yaWdpbmFsX2ZpbGVuYW1lID0gXCJEOlxcXFxwcm9qZWN0XFxcXGVyb3Mtd2FwXFxcXGJ1aWxkXFxcXHZpdGVcXFxcdmNvbnNvbGUudHNcIjtjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfaW1wb3J0X21ldGFfdXJsID0gXCJmaWxlOi8vL0Q6L3Byb2plY3QvZXJvcy13YXAvYnVpbGQvdml0ZS92Y29uc29sZS50c1wiO2ltcG9ydCBwYXRoIGZyb20gJ25vZGU6cGF0aCdcclxuaW1wb3J0IHsgdml0ZVZDb25zb2xlIH0gZnJvbSAndml0ZS1wbHVnaW4tdmNvbnNvbGUnXHJcblxyXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlVml0ZVZDb25zb2xlKHZpdGVFbnY6IGFueSkge1xyXG4gIGNvbnN0IHsgVklURV9BUFBfRU5WIH0gPSB2aXRlRW52XHJcbiAgcmV0dXJuIHZpdGVWQ29uc29sZSh7XHJcbiAgICBlbnRyeTogW3BhdGgucmVzb2x2ZSgnc3JjL21haW4udHMnKV0sXHJcbiAgICBlbmFibGVkOiBWSVRFX0FQUF9FTlYgIT09ICdwcm9kdWN0aW9uJyxcclxuICAgIGNvbmZpZzoge1xyXG4gICAgICBtYXhMb2dOdW1iZXI6IDEwMDAsXHJcbiAgICAgIHRoZW1lOiAnbGlnaHQnXHJcbiAgICB9LFxyXG4gICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL3ZhZHhxL3ZpdGUtcGx1Z2luLXZjb25zb2xlL2lzc3Vlcy8yMVxyXG4gICAgZHluYW1pY0NvbmZpZzoge1xyXG4gICAgICB0aGVtZTogYGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5jbGFzc0xpc3QuY29udGFpbnMoJ2RhcmsnKSA/ICdkYXJrJyA6ICdsaWdodCdgXHJcbiAgICB9LFxyXG4gICAgZXZlbnRMaXN0ZW5lcjogYFxyXG4gICAgICBjb25zdCB0YXJnZXRFbGVtZW50ID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvcignaHRtbCcpOyAvLyBcdTYyRTlcdTg5ODFcdTc2RDFcdTU0MkNcdTc2ODRcdTUxNDNcdTdEMjBcclxuICAgICAgY29uc3Qgb2JzZXJ2ZXJPcHRpb25zID0ge1xyXG4gICAgICAgIGF0dHJpYnV0ZXM6IHRydWUsIC8vIFx1NzZEMVx1NTQyQ1x1NUM1RVx1NjAyN1x1NTNEOFx1NTMxNlxyXG4gICAgICAgIGF0dHJpYnV0ZUZpbHRlcjogWydjbGFzcyddIC8vIFx1NTNFQVx1NzZEMVx1NTQyQ2NsYXNzXHU1QzVFXHU2MDI3XHU1M0Q4XHU1MzE2XHJcbiAgICAgIH07XHJcblxyXG4gICAgICAvLyBcdTVCOUFcdTRFNDlcdTU2REVcdThDMDNcdTUxRkRcdTY1NzBcdTY3NjVcdTU5MDRcdTc0MDZcdTg5QzJcdTVCREZcdTUyMzBcdTc2ODRcdTUzRDhcdTUzMTZcclxuICAgICAgZnVuY3Rpb24gaGFuZGxlQXR0cmlidXRlQ2hhbmdlKG11dGF0aW9uc0xpc3QpIHtcclxuICAgICAgICBmb3IobGV0IG11dGF0aW9uIG9mIG11dGF0aW9uc0xpc3QpIHtcclxuICAgICAgICAgIGlmIChtdXRhdGlvbi50eXBlID09PSAnYXR0cmlidXRlcycgJiYgbXV0YXRpb24uYXR0cmlidXRlTmFtZSA9PT0gJ2NsYXNzJykge1xyXG4gICAgICAgICAgICBpZiAod2luZG93ICYmIHdpbmRvdy52Q29uc29sZSkge1xyXG4gICAgICAgICAgICAgIHdpbmRvdy52Q29uc29sZS5keW5hbWljQ2hhbmdlLnZhbHVlID0gbmV3IERhdGUoKS5nZXRUaW1lKCk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIFx1NTIxQlx1NUVGQVx1ODlDMlx1NUJERlx1ODAwNVx1NUI5RVx1NEY4Qlx1NUU3Nlx1NEYyMFx1NTE2NVx1NTZERVx1OEMwM1x1NTFGRFx1NjU3MFxyXG4gICAgICBjb25zdCBvYnNlcnZlciA9IG5ldyBNdXRhdGlvbk9ic2VydmVyKGhhbmRsZUF0dHJpYnV0ZUNoYW5nZSk7XHJcblxyXG4gICAgICAvLyBcdTVGMDBcdTU5Q0JcdTg5QzJcdTVCREZcdTc2RUVcdTY4MDdcdTUxNDNcdTdEMjBcclxuICAgICAgb2JzZXJ2ZXIub2JzZXJ2ZSh0YXJnZXRFbGVtZW50LCBvYnNlcnZlck9wdGlvbnMpO1xyXG5cclxuICAgICAgLy8gXHU1RjUzXHU0RTBEXHU1MThEXHU5NzAwXHU4OTgxXHU4OUMyXHU1QkRGXHU2NUY2XHVGRjBDXHU1MDVDXHU2QjYyXHU4OUMyXHU1QkRGXHJcbiAgICAgIC8vIG9ic2VydmVyLmRpc2Nvbm5lY3QoKTtcclxuICAgIGBcclxuICB9KVxyXG59XHJcbiJdLAogICJtYXBwaW5ncyI6ICI7QUFBaVAsT0FBT0EsV0FBVTtBQUNsUSxTQUFTLGVBQWU7QUFFeEIsT0FBTyxjQUFjO0FBQ3JCLE9BQU8sa0JBQWtCOzs7QUNKaVAsU0FBUyxTQUFTLGVBQWU7QUFDM1MsU0FBUyxxQkFBcUI7QUFHOUIsT0FBTyxTQUFTO0FBSWhCLE9BQU8sZ0JBQWdCO0FBQ3ZCLFNBQVMsa0JBQWtCLG9CQUFvQjtBQUMvQyxPQUFPLGdCQUFnQjtBQUN2QixTQUFTLDRCQUE0QjtBQUNyQyxPQUFPLGVBQWU7QUFDdEIsT0FBTyx5QkFBeUI7QUFDaEMsU0FBUyxrQkFBa0I7QUFHM0IsT0FBTyxpQkFBaUI7QUFDeEIsT0FBTyxtQkFBbUI7OztBQ2xCc1AsT0FBTyxVQUFVO0FBQ2pTLFNBQVMsb0JBQW9CO0FBRXRCLFNBQVMsbUJBQW1CLFNBQWM7QUFDL0MsUUFBTSxFQUFFLGFBQWEsSUFBSTtBQUN6QixTQUFPLGFBQWE7QUFBQSxJQUNsQixPQUFPLENBQUMsS0FBSyxRQUFRLGFBQWEsQ0FBQztBQUFBLElBQ25DLFNBQVMsaUJBQWlCO0FBQUEsSUFDMUIsUUFBUTtBQUFBLE1BQ04sY0FBYztBQUFBLE1BQ2QsT0FBTztBQUFBLElBQ1Q7QUFBQTtBQUFBLElBRUEsZUFBZTtBQUFBLE1BQ2IsT0FBTztBQUFBLElBQ1Q7QUFBQSxJQUNBLGVBQWU7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUEsRUEyQmpCLENBQUM7QUFDSDs7O0FENUN1SyxJQUFNLDJDQUEyQztBQXNCak4sU0FBUyxrQkFBa0IsU0FBYyxVQUFVLE9BQU87QUFDL0QsU0FBTztBQUFBO0FBQUE7QUFBQSxJQUdMLFVBQVU7QUFBQSxNQUNSLFlBQVksQ0FBQyxNQUFNO0FBQUEsTUFDbkIsY0FBYztBQUFBLE1BQ2QsS0FBSztBQUFBLElBQ1AsQ0FBQztBQUFBLElBRUQsSUFBSTtBQUFBLE1BQ0YsVUFBVTtBQUFBLFFBQ1IsaUJBQWlCO0FBQUEsVUFDZixpQkFBaUIsQ0FBQyxRQUFRLElBQUksV0FBVyxTQUFTO0FBQUEsUUFDcEQ7QUFBQSxNQUNGO0FBQUEsSUFDRixDQUFDO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQSxJQUtELFdBQVc7QUFBQTtBQUFBLElBRVgsb0JBQW9CO0FBQUE7QUFBQSxJQUdwQixXQUFXO0FBQUEsTUFDVCxZQUFZLENBQUMsS0FBSztBQUFBLE1BQ2xCLFdBQVcsQ0FBQyxhQUFhLEdBQUcsaUJBQWlCLENBQUM7QUFBQSxNQUM5QyxTQUFTLENBQUMsVUFBVSxZQUFZO0FBQUEsTUFDaEMsS0FBSztBQUFBLElBQ1AsQ0FBQztBQUFBO0FBQUEsSUFHRCxXQUFXO0FBQUEsTUFDVCxTQUFTLENBQUMsY0FBYyxVQUFVLGNBQWMsT0FBTztBQUFBLE1BQ3ZELFNBQVM7QUFBQSxRQUNQO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxVQUNFLG1CQUFtQixDQUFDLFNBQVM7QUFBQSxVQUM3QixnQkFBZ0IsQ0FBQyxRQUFRLFFBQVE7QUFBQSxVQUNqQyxZQUFZLENBQUMsU0FBUztBQUFBLFFBQ3hCO0FBQUE7QUFBQSxNQUVGO0FBQUEsTUFDQSxLQUFLO0FBQUEsSUFDUCxDQUFDO0FBQUE7QUFBQSxJQUdELGNBQWM7QUFBQTtBQUFBLE1BRVosU0FBUyxRQUFRLFFBQVEsY0FBYyx3Q0FBZSxDQUFDLEdBQUcsc0JBQXNCO0FBQUEsSUFDbEYsQ0FBQztBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUEsSUFXRCxtQkFBbUIsT0FBTztBQUFBO0FBQUEsSUFHMUIsWUFBWTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUEsRUE4QmQ7QUFDRjs7O0FEM0hBLElBQU0sbUNBQW1DO0FBUXpDLElBQU8sc0JBQVEsQ0FBQyxFQUFFLE1BQU0sUUFBUSxNQUE2QjtBQUMzRCxRQUFNLE9BQU8sUUFBUSxJQUFJO0FBQ3pCLFFBQU0sTUFBTSxRQUFRLE1BQU0sSUFBSTtBQUM5QixRQUFNLEVBQUUsY0FBYyxxQkFBcUIsSUFBSTtBQUUvQyxTQUFPO0FBQUEsSUFDTCxNQUFNO0FBQUEsSUFDTixTQUFTLGtCQUFrQixLQUFLLFlBQVksT0FBTztBQUFBLElBQ25ELFNBQVM7QUFBQSxNQUNQLE1BQU0saUJBQWlCLFNBQVMsQ0FBQyxJQUFJLENBQUMsYUFBYTtBQUFBLElBQ3JEO0FBQUEsSUFDQSxRQUFRO0FBQUEsTUFDTixNQUFNO0FBQUEsTUFDTixNQUFNO0FBQUEsTUFDTixPQUFPO0FBQUEsUUFDTCxRQUFRO0FBQUE7QUFBQSxVQUVOLFFBQVE7QUFBQSxVQUNSLElBQUk7QUFBQSxVQUNKLGNBQWM7QUFBQTtBQUFBLFFBRWhCO0FBQUEsTUFDRjtBQUFBLElBQ0Y7QUFBQSxJQUNBLFNBQVM7QUFBQSxNQUNQLE9BQU87QUFBQSxRQUNMLE1BQU1DLE1BQUssS0FBSyxrQ0FBVyxPQUFPO0FBQUEsUUFDbEMsS0FBS0EsTUFBSyxLQUFLLGtDQUFXLE9BQU87QUFBQSxRQUNqQyxLQUFLQSxNQUFLLEtBQUssa0NBQVcsY0FBYztBQUFBLE1BQzFDO0FBQUEsSUFDRjtBQUFBLElBQ0EsS0FBSztBQUFBLE1BQ0gscUJBQXFCO0FBQUEsUUFDbkIsTUFBTTtBQUFBLFVBQ0osZ0JBQWdCO0FBQUEsUUFDbEI7QUFBQSxNQUNGO0FBQUEsTUFDQSxTQUFTO0FBQUEsUUFDUCxTQUFTO0FBQUEsVUFDUCxhQUFhO0FBQUE7QUFBQSxVQUViLFNBQVM7QUFBQSxZQUNQLGFBQWE7QUFBQSxZQUNiLGVBQWU7QUFBQSxZQUNmLGlCQUFpQjtBQUFBLFlBQ2pCLG1CQUFtQixDQUFDLFVBQVUsaUJBQWlCLFdBQVc7QUFBQSxZQUMxRCxtQkFBbUIsQ0FBQyxzQkFBc0IsZ0JBQWdCLGFBQWE7QUFBQSxZQUN2RSxpQ0FBaUMsQ0FBQyxjQUFjLGFBQWEsZUFBZSxTQUFTO0FBQUEsVUFDdkYsQ0FBQztBQUFBLFFBQ0g7QUFBQSxNQUNGO0FBQUEsSUFDRjtBQUFBLEVBQ0Y7QUFDRjsiLAogICJuYW1lcyI6IFsicGF0aCIsICJwYXRoIl0KfQo=
